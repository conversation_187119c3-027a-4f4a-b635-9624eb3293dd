2025-07-11 10:04:11,318 - INFO - 开始分析所有保守序列文件
2025-07-11 10:04:11,318 - INFO - 找到 4 个FASTA文件
2025-07-11 10:04:11,318 - INFO - 分析文件: conserved_sequences_test/gyrA_sequences.fasta
2025-07-11 10:04:11,331 - WARNING - matplotlib/seaborn未安装，跳过可视化
2025-07-11 10:04:11,331 - INFO - 分析文件: conserved_sequences_test/tRNA_sequences.fasta
2025-07-11 10:04:11,333 - WARNING - matplotlib/seaborn未安装，跳过可视化
2025-07-11 10:04:11,333 - INFO - 分析文件: conserved_sequences_test/16S_rRNA_sequences.fasta
2025-07-11 10:04:11,335 - WARNING - matplotlib/seaborn未安装，跳过可视化
2025-07-11 10:04:11,335 - INFO - 分析文件: conserved_sequences_test/all_conserved_sequences.fasta
2025-07-11 10:04:11,339 - WARNING - matplotli<PERSON>/seaborn未安装，跳过可视化
2025-07-11 10:04:11,340 - INFO - 生成综合报告: sequence_analysis_test/comprehensive_analysis_report.txt
2025-07-11 10:04:11,345 - INFO - 导出详细数据: sequence_analysis_test/sequence_details.csv
2025-07-11 10:04:11,410 - INFO - 导出汇总统计: sequence_analysis_test/summary_statistics.csv
