# 保守序列提取系统使用指南

## 📋 概述

本系统根据metadata-v2.tsv文件中的genome_id，从下载的基因组数据中提取保守序列，包括16S rRNA、23S rRNA、tRNA等重要的保守基因。

## 🚀 快速开始

### 1. 安装依赖
```bash
python install_dependencies.py
```

### 2. 测试提取（推荐先运行）
```bash
python extract_conserved_sequences.py data_downloads/metadata-v2.tsv --max-genomes 5
```

### 3. 完整提取
```bash
python extract_conserved_sequences.py data_downloads/metadata-v2.tsv
```

### 4. 分析序列
```bash
python analyze_conserved_sequences.py --sequences-dir conserved_sequences
```

## 📁 输入文件要求

### metadata-v2.tsv格式
```
genome_id	optimal_temperature	taxid	kingdom
GCA_000219855.1	50.0	1051632	Bacteria
GCA_000283575.1	30.0	693746	Bacteria
...
```

### 基因组数据结构
```
data_downloads/
├── Bacteria/
│   └── GCA_000219855.1/
│       ├── GCA_000219855.1_genomic.fna.gz
│       └── GCA_000219855.1_genomic.gff.gz
├── Archaea/
├── Fungi/
└── ...
```

## 🧬 提取的保守序列类型

| 基因类型 | 描述 | 用途 |
|---------|------|------|
| 16S rRNA | 小亚基核糖体RNA | 系统发育分析、物种鉴定 |
| 23S rRNA | 大亚基核糖体RNA | 系统发育分析 |
| 5S rRNA | 5S核糖体RNA | 结构分析 |
| tRNA | 转移RNA | 功能分析 |
| rpoB | RNA聚合酶β亚基 | 物种鉴定 |
| recA | 重组酶A | 进化分析 |
| gyrA | DNA回转酶A亚基 | 抗性研究 |
| dnaK | 分子伴侣 | 功能研究 |

## 📊 输出文件说明

### 序列文件
- `16S_rRNA_sequences.fasta` - 16S rRNA序列
- `tRNA_sequences.fasta` - tRNA序列
- `gyrA_sequences.fasta` - gyrA序列
- `all_conserved_sequences.fasta` - 所有保守序列

### 报告文件
- `extraction_report.txt` - 提取统计报告
- `extraction_log_*.txt` - 详细处理日志

### 分析文件（运行分析脚本后）
- `comprehensive_analysis_report.txt` - 综合分析报告
- `sequence_details.csv` - 详细序列信息
- `summary_statistics.csv` - 汇总统计数据

## 🔧 命令行参数

### extract_conserved_sequences.py
```bash
python extract_conserved_sequences.py [metadata_file] [选项]

必需参数:
  metadata_file         metadata-v2.tsv文件路径

可选参数:
  --base-dir DIR        基因组数据目录 (默认: data_downloads)
  --output-dir DIR      输出目录 (默认: conserved_sequences)
  --max-genomes N       最大处理基因组数量（用于测试）
  --help               显示帮助信息
```

### analyze_conserved_sequences.py
```bash
python analyze_conserved_sequences.py [选项]

可选参数:
  --sequences-dir DIR   保守序列目录 (默认: conserved_sequences_test)
  --output-dir DIR      分析结果输出目录 (默认: sequence_analysis)
  --help               显示帮助信息
```

## 📈 使用示例

### 示例1: 基本提取
```bash
# 提取前10个基因组的保守序列
python extract_conserved_sequences.py data_downloads/metadata-v2.tsv \
    --max-genomes 10 \
    --output-dir test_sequences
```

### 示例2: 完整提取
```bash
# 提取所有基因组的保守序列
python extract_conserved_sequences.py data_downloads/metadata-v2.tsv \
    --output-dir full_sequences
```

### 示例3: 序列分析
```bash
# 分析提取的序列
python analyze_conserved_sequences.py \
    --sequences-dir full_sequences \
    --output-dir sequence_analysis
```

### 示例4: 交互式使用
```bash
# 运行交互式示例
python extract_conserved_example.py
```

## 📊 测试结果示例

### 提取统计
```
保守序列提取报告
==================================================

处理时间: 2025-07-11 10:01:09
总基因组数: 3
成功处理: 2
成功率: 66.7%

提取的保守序列统计:
------------------------------
16S_rRNA: 18 个序列
tRNA: 52 个序列
gyrA: 5 个序列
```

### 分析结果
```
各基因类型详细统计:
------------------------------

16S_rRNA:
  序列数量: 18
  基因组数: 10
  长度范围: 540-1534 bp
  平均长度: 1442.5 ± 262.0 bp
  GC含量范围: 53.0-64.6%
  平均GC含量: 60.2 ± 5.3%
```

## 🔍 故障排除

### 常见问题

#### 1. 依赖包安装失败
```bash
# 手动安装
pip install biopython pandas numpy

# 或使用conda
conda install biopython pandas numpy
```

#### 2. 找不到基因组文件
- 检查metadata-v2.tsv中的genome_id是否正确
- 确认data_downloads目录结构是否正确
- 检查文件权限

#### 3. 提取序列数量少
- 某些基因组可能缺少注释文件
- 注释文件格式可能不标准
- 可以查看日志文件了解详细信息

#### 4. 内存不足
- 减少max-genomes参数
- 分批处理大量基因组

### 日志分析
查看详细日志文件：
```bash
tail -f conserved_sequences/extraction_log_*.txt
```

## 🧪 高级用法

### 自定义保守基因
修改`extract_conserved_sequences.py`中的`conserved_genes`字典：
```python
self.conserved_genes = {
    'custom_gene': ['custom gene name', 'alternative name'],
    # 添加更多自定义基因
}
```

### 批量处理
```bash
# 处理多个metadata文件
for file in metadata_*.tsv; do
    python extract_conserved_sequences.py "$file" --output-dir "sequences_$(basename $file .tsv)"
done
```

### 结果合并
```bash
# 合并多个结果目录
cat sequences_*/16S_rRNA_sequences.fasta > combined_16S_rRNA.fasta
```

## 📚 后续分析建议

### 系统发育分析
1. 使用16S rRNA序列进行多序列比对
2. 构建系统发育树
3. 分析物种间的进化关系

### 物种鉴定
1. 将未知序列与提取的保守序列比对
2. 使用BLAST进行相似性搜索
3. 基于相似度进行物种鉴定

### 功能分析
1. 分析特定功能基因的变异
2. 研究基因功能与环境的关系
3. 预测蛋白质功能

## 🔗 相关工具

### 序列分析工具
- **MEGA** - 分子进化遗传学分析
- **BLAST** - 序列相似性搜索
- **ClustalW** - 多序列比对

### 系统发育工具
- **IQ-TREE** - 最大似然法建树
- **RAxML** - 快速最大似然法
- **MrBayes** - 贝叶斯推断

### 数据库
- **SILVA** - rRNA数据库
- **RDP** - 核糖体数据库项目
- **NCBI** - 国家生物技术信息中心

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件了解详细错误信息
2. 检查输入文件格式是否正确
3. 确认所有依赖包已正确安装
4. 参考本文档的故障排除部分

## 🎯 总结

本系统提供了完整的保守序列提取和分析流程，支持：
- 多种保守基因类型的自动识别和提取
- 大规模基因组数据的批量处理
- 详细的统计分析和可视化
- 灵活的参数配置和输出格式

适用于系统发育分析、物种鉴定、比较基因组学等多种研究场景。
