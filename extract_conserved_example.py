#!/usr/bin/env python3
"""
保守序列提取示例脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    try:
        from Bio import SeqIO
        import pandas as pd
        print("✅ 依赖包检查通过")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("请先运行: python install_dependencies.py")
        return False

def run_extraction_example():
    """运行保守序列提取示例"""
    
    print("=== 保守序列提取示例 ===\n")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查输入文件
    metadata_file = "data_downloads/metadata-v2.tsv"
    if not os.path.exists(metadata_file):
        print(f"错误: metadata文件不存在: {metadata_file}")
        print("请确保metadata-v2.tsv文件存在")
        return
    
    print(f"输入文件: {metadata_file}")
    
    # 检查基因组数据目录
    data_dir = "data_downloads"
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录不存在: {data_dir}")
        return
    
    # 统计可用的基因组数量
    genome_count = 0
    for subdir in ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']:
        subdir_path = Path(data_dir) / subdir
        if subdir_path.exists():
            genome_count += len([d for d in subdir_path.iterdir() if d.is_dir()])
    
    print(f"发现 {genome_count} 个基因组目录")
    
    # 询问用户是否进行测试提取
    response = input("是否进行测试提取 (前5个基因组)? [y/N]: ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n开始测试提取前5个基因组的保守序列...")
        
        # 构建命令
        cmd = [
            sys.executable,
            "extract_conserved_sequences.py",
            metadata_file,
            "--max-genomes", "5",
            "--output-dir", "conserved_sequences_test"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 执行提取
            result = subprocess.run(cmd, check=True)
            print("\n测试提取完成!")
            
            # 检查输出
            output_dir = Path("conserved_sequences_test")
            if output_dir.exists():
                output_files = list(output_dir.glob("*.fasta"))
                if output_files:
                    print(f"生成了 {len(output_files)} 个序列文件:")
                    for file in output_files:
                        print(f"  - {file}")
                else:
                    print("未生成序列文件，可能没有找到保守序列")
                
                # 检查报告文件
                report_file = output_dir / "extraction_report.txt"
                if report_file.exists():
                    print(f"\n提取报告: {report_file}")
                    with open(report_file, 'r', encoding='utf-8') as f:
                        print(f.read())
            
        except subprocess.CalledProcessError as e:
            print(f"提取失败: {e}")
            return
        except KeyboardInterrupt:
            print("\n用户中断提取")
            return
    
    else:
        print("跳过测试提取")
    
    # 询问是否进行完整提取
    print(f"\n是否进行完整提取? 这将处理metadata-v2.tsv中的所有基因组")
    print("注意: 这可能需要很长时间，建议先进行测试")
    
    response = input("继续完整提取? [y/N]: ").strip().lower()
    
    if response in ['y', 'yes']:
        print("\n开始完整提取...")
        print("注意: 这可能需要几小时，建议在稳定的环境下运行")
        
        # 构建完整提取命令
        cmd = [
            sys.executable,
            "extract_conserved_sequences.py",
            metadata_file,
            "--output-dir", "conserved_sequences_full"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        try:
            # 执行完整提取
            result = subprocess.run(cmd, check=True)
            print("\n完整提取完成!")
            
        except subprocess.CalledProcessError as e:
            print(f"提取失败: {e}")
            return
        except KeyboardInterrupt:
            print("\n用户中断提取")
            return
    
    else:
        print("跳过完整提取")
    
    print("\n=== 提取示例结束 ===")
    print("\n使用说明:")
    print("1. 测试提取: python extract_conserved_sequences.py data_downloads/metadata-v2.tsv --max-genomes 5")
    print("2. 完整提取: python extract_conserved_sequences.py data_downloads/metadata-v2.tsv")
    print("3. 自定义输出: python extract_conserved_sequences.py data_downloads/metadata-v2.tsv --output-dir my_sequences")
    print("4. 查看帮助: python extract_conserved_sequences.py --help")

def show_sequence_analysis_tips():
    """显示序列分析建议"""
    print("\n=== 序列分析建议 ===")
    print("提取的保守序列可用于:")
    print("1. 系统发育分析 - 使用16S rRNA或23S rRNA序列")
    print("2. 物种鉴定 - 比较保守基因序列")
    print("3. 进化分析 - 分析保守区域的变异")
    print("4. 功能注释 - 基于保守基因预测功能")
    print("\n推荐工具:")
    print("- MEGA: 系统发育分析")
    print("- BLAST: 序列比对和相似性搜索")
    print("- ClustalW/MUSCLE: 多序列比对")
    print("- RAxML/IQ-TREE: 最大似然法建树")

if __name__ == "__main__":
    run_extraction_example()
    show_sequence_analysis_tips()
