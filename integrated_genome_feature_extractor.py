#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成的基因组特征提取器
结合基因组基本特征和GO注释功能特征

主要功能：
1. 整合基因组序列特征和GO功能注释特征
2. 支持多线程并发处理
3. 支持断点恢复机制
4. 生成综合的特征数据集

作者: AI Assistant
日期: 2025-07-14
"""

import os
import sys
import json
import argparse
import pandas as pd
import numpy as np
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 导入自定义模块
from optimized_genome_feature_extractor import GenomeFeatureExtractor, GenomeProcessor
from go_annotation_feature_extractor import GOGenomeProcessor, GODatabase, GOAnnotationParser, GOFeatureExtractor

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class IntegratedGenomeFeatureExtractor:
    """集成的基因组特征提取器"""
    
    def __init__(self, base_dir: str = "data_downloads", output_dir: str = "integrated_features_output",
                 max_workers: int = 4, checkpoint_interval: int = 50):
        self.base_dir = Path(base_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 多线程配置
        self.max_workers = max_workers
        self.thread_lock = threading.Lock()
        self.checkpoint_interval = checkpoint_interval
        
        # 初始化子提取器
        self.genome_extractor = GenomeFeatureExtractor()
        
        # GO相关组件
        self.go_db = GODatabase(cache_dir=self.output_dir / "go_cache")
        self.go_parser = GOAnnotationParser(self.go_db)
        self.go_extractor = GOFeatureExtractor(self.go_db)
        
        # 进度跟踪
        self.progress_file = self.output_dir / "integrated_extraction_progress.json"
        self.completed_genomes = set()
        self.load_progress()
        
        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'processed_genomes': 0,
            'failed_genomes': 0,
            'genomes_with_basic_features': 0,
            'genomes_with_go_features': 0,
            'genomes_with_both_features': 0,
            'errors': []
        }
        
        # 设置日志
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / f"integrated_extraction_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        # 添加到logger
        logger.addHandler(file_handler)
        
        self.log_file = log_file
    
    def load_progress(self):
        """加载进度文件"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    self.completed_genomes = set(progress_data.get('completed_genomes', []))
                logger.info(f"加载进度: 已完成 {len(self.completed_genomes)} 个基因组")
            except Exception as e:
                logger.warning(f"加载进度文件失败: {e}")
    
    def save_progress(self):
        """保存进度"""
        try:
            progress_data = {
                'completed_genomes': list(self.completed_genomes),
                'last_update': datetime.now().isoformat(),
                'stats': dict(self.stats)
            }
            with open(self.progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def find_genome_files(self, genome_id: str) -> Tuple[Optional[str], Optional[str]]:
        """查找基因组文件和注释文件"""
        # 在各个子目录中搜索
        for subdir in ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']:
            genome_dir = self.base_dir / subdir / genome_id
            
            if genome_dir.exists():
                # 查找基因组序列文件
                fna_file = genome_dir / f"{genome_id}_genomic.fna.gz"
                if not fna_file.exists():
                    fna_file = genome_dir / f"{genome_id}_genomic.fna"
                
                # 查找注释文件
                gff_file = genome_dir / f"{genome_id}_genomic.gff.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gff"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf"
                
                return (str(fna_file) if fna_file.exists() else None,
                       str(gff_file) if gff_file.exists() else None)
        
        return None, None
    
    def process_single_genome(self, genome_data: Tuple[int, pd.Series]) -> Dict:
        """处理单个基因组的所有特征"""
        idx, row = genome_data
        genome_id = row['genome_id']
        
        # 检查是否已完成
        if genome_id in self.completed_genomes:
            return {'genome_id': genome_id, 'status': 'skipped', 'reason': 'already_completed'}
        
        try:
            thread_id = threading.current_thread().ident
            logger.info(f"[线程{thread_id}] 处理基因组: {genome_id}")
            
            # 查找文件
            genome_file, annotation_file = self.find_genome_files(genome_id)
            
            if not genome_file:
                return {
                    'genome_id': genome_id,
                    'status': 'failed',
                    'error': 'Genome file not found',
                    'features': {}
                }
            
            # 初始化特征字典
            integrated_features = {}
            
            # 添加元数据
            integrated_features['genome_id'] = genome_id
            if 'optimal_temperature' in row:
                integrated_features['optimal_temperature'] = row['optimal_temperature']
            if 'kingdom' in row:
                integrated_features['kingdom'] = row['kingdom']
            
            has_basic_features = False
            has_go_features = False
            
            # 1. 提取基因组基本特征
            try:
                if annotation_file:
                    basic_features = self.genome_extractor.extract_all_features(genome_file, annotation_file)
                    if basic_features:
                        # 添加前缀以区分特征类型
                        for key, value in basic_features.items():
                            if key not in ['genome_id', 'optimal_temperature', 'kingdom']:
                                integrated_features[f'basic_{key}'] = value
                        has_basic_features = True
                        logger.info(f"[线程{thread_id}] 成功提取基因组基本特征: {len(basic_features)} 个")
                else:
                    logger.warning(f"[线程{thread_id}] 无注释文件，跳过基因组基本特征提取")
            except Exception as e:
                logger.warning(f"[线程{thread_id}] 基因组基本特征提取失败: {e}")
            
            # 2. 提取GO功能特征
            try:
                if annotation_file:
                    # 解析GO注释
                    go_annotations = self.go_parser.parse_gff_go_annotations(annotation_file)
                    
                    # 提取GO特征
                    go_features = self.go_extractor.extract_go_features(go_annotations)
                    
                    if go_features:
                        # 添加前缀以区分特征类型
                        for key, value in go_features.items():
                            if key not in ['genome_id', 'optimal_temperature', 'kingdom']:
                                integrated_features[f'go_{key}'] = value
                        has_go_features = True
                        logger.info(f"[线程{thread_id}] 成功提取GO功能特征: {len(go_features)} 个")
                else:
                    logger.warning(f"[线程{thread_id}] 无注释文件，跳过GO特征提取")
            except Exception as e:
                logger.warning(f"[线程{thread_id}] GO特征提取失败: {e}")
            
            # 更新统计信息
            with self.thread_lock:
                self.stats['processed_genomes'] += 1
                
                if has_basic_features:
                    self.stats['genomes_with_basic_features'] += 1
                if has_go_features:
                    self.stats['genomes_with_go_features'] += 1
                if has_basic_features and has_go_features:
                    self.stats['genomes_with_both_features'] += 1
                
                # 标记为已完成
                self.completed_genomes.add(genome_id)
                
                # 定期保存进度
                if len(self.completed_genomes) % self.checkpoint_interval == 0:
                    self.save_progress()
                    self.go_db.save_cached_data()  # 保存GO缓存
            
            return {
                'genome_id': genome_id,
                'status': 'success',
                'error': None,
                'features': integrated_features,
                'has_basic_features': has_basic_features,
                'has_go_features': has_go_features
            }
            
        except Exception as e:
            error_msg = f"处理基因组 {genome_id} 时出错: {e}"
            logger.error(f"[线程{thread_id}] {error_msg}")
            
            with self.thread_lock:
                self.stats['failed_genomes'] += 1
                self.stats['errors'].append(error_msg)
            
            return {
                'genome_id': genome_id,
                'status': 'failed',
                'error': str(e),
                'features': {}
            }

    def process_metadata_file(self, metadata_file: str, output_file: str = None,
                            max_genomes: int = None, use_threading: bool = True) -> str:
        """处理metadata文件，提取所有基因组的集成特征"""
        logger.info(f"开始集成特征提取: {metadata_file}")
        logger.info(f"多线程模式: {'启用' if use_threading else '禁用'} (工作线程: {self.max_workers})")

        # 读取metadata文件
        try:
            df = pd.read_csv(metadata_file, sep='\t')
            logger.info(f"读取到 {len(df)} 个基因组")
        except Exception as e:
            logger.error(f"读取metadata文件失败: {e}")
            return None

        # 限制处理数量
        if max_genomes:
            df = df.head(max_genomes)
            logger.info(f"限制处理前 {max_genomes} 个基因组")

        self.stats['total_genomes'] = len(df)

        # 过滤已完成的基因组
        remaining_df = df[~df['genome_id'].isin(self.completed_genomes)]
        logger.info(f"剩余待处理: {len(remaining_df)} 个基因组")

        if remaining_df.empty:
            logger.info("所有基因组已处理完成!")
            return self._load_existing_results(output_file)

        # 处理数据
        all_features = []

        if use_threading and self.max_workers > 1:
            all_features = self._process_with_threading(remaining_df)
        else:
            all_features = self._process_sequentially(remaining_df)

        # 合并已有结果
        existing_features = self._load_existing_results()
        if existing_features:
            all_features.extend(existing_features)

        # 保存结果
        if all_features:
            if output_file is None:
                output_file = self.output_dir / f"integrated_features_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            features_df = pd.DataFrame(all_features)
            features_df.to_csv(output_file, index=False)
            logger.info(f"集成特征数据已保存到: {output_file}")

            # 生成统计报告
            self._generate_report(str(output_file).replace('.csv', '_report.txt'))

            # 最终保存进度和缓存
            self.save_progress()
            self.go_db.save_cached_data()

            return str(output_file)
        else:
            logger.error("没有成功提取任何集成特征")
            return None

    def _process_with_threading(self, df: pd.DataFrame) -> List[Dict]:
        """多线程处理"""
        logger.info(f"使用多线程处理，工作线程数: {self.max_workers}")

        all_features = []

        # 准备任务
        tasks = [(idx, row) for idx, row in df.iterrows()]

        # 使用线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_task = {executor.submit(self.process_single_genome, task): task for task in tasks}

            completed_count = 0
            for future in as_completed(future_to_task):
                try:
                    result = future.result()
                    completed_count += 1

                    if result['status'] == 'success':
                        all_features.append(result['features'])

                    # 进度报告
                    if completed_count % 10 == 0:
                        progress = (completed_count / len(tasks)) * 100
                        logger.info(f"进度: {completed_count}/{len(tasks)} ({progress:.1f}%)")

                except Exception as e:
                    logger.error(f"线程任务执行失败: {e}")

        return all_features

    def _process_sequentially(self, df: pd.DataFrame) -> List[Dict]:
        """顺序处理"""
        logger.info("使用顺序处理模式")

        all_features = []

        for idx, (_, row) in enumerate(df.iterrows()):
            result = self.process_single_genome((idx, row))

            if result['status'] == 'success':
                all_features.append(result['features'])

            # 进度报告
            if (idx + 1) % 10 == 0:
                progress = ((idx + 1) / len(df)) * 100
                logger.info(f"进度: {idx + 1}/{len(df)} ({progress:.1f}%)")

        return all_features

    def _load_existing_results(self, output_file: str = None) -> List[Dict]:
        """加载已有的结果"""
        existing_features = []

        # 查找已有的结果文件
        if output_file and os.path.exists(output_file):
            result_files = [output_file]
        else:
            result_files = list(self.output_dir.glob("integrated_features_*.csv"))

        for result_file in result_files:
            try:
                df = pd.read_csv(result_file)
                existing_features.extend(df.to_dict('records'))
                logger.info(f"加载已有结果: {len(df)} 个基因组从 {result_file}")
            except Exception as e:
                logger.warning(f"加载结果文件失败 {result_file}: {e}")

        return existing_features

    def _generate_report(self, report_file: str):
        """生成处理报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("集成基因组特征提取报告\n")
            f.write("=" * 70 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总基因组数: {self.stats['total_genomes']}\n")
            f.write(f"成功处理: {self.stats['processed_genomes']}\n")
            f.write(f"失败数量: {self.stats['failed_genomes']}\n")
            f.write(f"成功率: {(self.stats['processed_genomes']/max(self.stats['total_genomes'], 1)*100):.1f}%\n\n")

            f.write("特征提取统计:\n")
            f.write("-" * 40 + "\n")
            f.write(f"有基因组基本特征: {self.stats['genomes_with_basic_features']}\n")
            f.write(f"有GO功能特征: {self.stats['genomes_with_go_features']}\n")
            f.write(f"同时有两种特征: {self.stats['genomes_with_both_features']}\n")

            if self.stats['processed_genomes'] > 0:
                basic_coverage = (self.stats['genomes_with_basic_features'] / self.stats['processed_genomes']) * 100
                go_coverage = (self.stats['genomes_with_go_features'] / self.stats['processed_genomes']) * 100
                both_coverage = (self.stats['genomes_with_both_features'] / self.stats['processed_genomes']) * 100

                f.write(f"基本特征覆盖率: {basic_coverage:.1f}%\n")
                f.write(f"GO特征覆盖率: {go_coverage:.1f}%\n")
                f.write(f"完整特征覆盖率: {both_coverage:.1f}%\n")

            f.write(f"\nGO数据库统计:\n")
            f.write("-" * 40 + "\n")
            f.write(f"缓存的GO术语数: {len(self.go_db.go_terms)}\n")

            if self.stats['errors']:
                f.write(f"\n错误信息:\n")
                f.write("-" * 40 + "\n")
                for error in self.stats['errors'][:10]:  # 只显示前10个错误
                    f.write(f"{error}\n")
                if len(self.stats['errors']) > 10:
                    f.write(f"... 还有 {len(self.stats['errors']) - 10} 个错误\n")

        logger.info(f"生成处理报告: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='集成的基因组特征提取器')
    parser.add_argument('metadata_file', help='metadata-v2.tsv文件路径')
    parser.add_argument('--base-dir', default='data_downloads', help='基因组数据目录')
    parser.add_argument('--output-dir', default='integrated_features_output', help='输出目录')
    parser.add_argument('--output-file', help='输出文件路径')
    parser.add_argument('--max-genomes', type=int, help='最大处理基因组数量（用于测试）')
    parser.add_argument('--max-workers', type=int, default=4, help='最大工作线程数')
    parser.add_argument('--no-threading', action='store_true', help='禁用多线程')
    parser.add_argument('--checkpoint-interval', type=int, default=50, help='检查点保存间隔')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.metadata_file):
        logger.error(f"输入文件不存在: {args.metadata_file}")
        sys.exit(1)

    # 创建处理器
    processor = IntegratedGenomeFeatureExtractor(
        base_dir=args.base_dir,
        output_dir=args.output_dir,
        max_workers=args.max_workers,
        checkpoint_interval=args.checkpoint_interval
    )

    # 开始处理
    try:
        output_file = processor.process_metadata_file(
            args.metadata_file,
            output_file=args.output_file,
            max_genomes=args.max_genomes,
            use_threading=not args.no_threading
        )

        if output_file:
            logger.info(f"集成特征提取完成! 结果保存在: {output_file}")
        else:
            logger.error("集成特征提取失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断处理")
        processor.save_progress()
        processor.go_db.save_cached_data()
        print("进度已保存，可以稍后继续")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        processor.save_progress()
        processor.go_db.save_cached_data()
        sys.exit(1)

if __name__ == "__main__":
    main()
