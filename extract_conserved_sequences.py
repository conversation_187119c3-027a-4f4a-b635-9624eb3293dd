#!/usr/bin/env python3
"""
根据metadata-v2.tsv中的genome_id提取每个物种的保守序列
主要提取16S rRNA、23S rRNA、tRNA等保守基因序列
"""

import pandas as pd
import gzip
import os
import re
from pathlib import Path
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
import argparse
import logging
from datetime import datetime

class ConservedSequenceExtractor:
    def __init__(self, base_dir="data_downloads", output_dir="conserved_sequences"):
        self.base_dir = Path(base_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 保守基因的关键词
        self.conserved_genes = {
            '16S_rRNA': ['16S ribosomal RNA', '16S rRNA', 'small subunit ribosomal RNA'],
            '23S_rRNA': ['23S ribosomal RNA', '23S rRNA', 'large subunit ribosomal RNA'],
            '5S_rRNA': ['5S ribosomal RNA', '5S rRNA'],
            'tRNA': ['tRNA', 'transfer RNA'],
            'rpoB': ['RNA polymerase beta subunit', 'rpoB'],
            'recA': ['recombinase A', 'recA'],
            'gyrA': ['DNA gyrase subunit A', 'gyrA'],
            'dnaK': ['molecular chaperone DnaK', 'dnaK']
        }
        
        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'processed_genomes': 0,
            'found_sequences': {},
            'errors': []
        }
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / f"extraction_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def find_genome_files(self, genome_id, kingdom):
        """查找基因组文件"""
        # 根据kingdom确定目录
        if kingdom == 'Bacteria':
            genome_dir = self.base_dir / 'Bacteria' / genome_id
        elif kingdom == 'Archaea':
            genome_dir = self.base_dir / 'Archaea' / genome_id
        elif kingdom == 'Eukaryota':
            genome_dir = self.base_dir / 'Fungi' / genome_id
        else:
            # 尝试在所有目录中查找
            for subdir in ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']:
                test_dir = self.base_dir / subdir / genome_id
                if test_dir.exists():
                    genome_dir = test_dir
                    break
            else:
                return None, None
        
        if not genome_dir.exists():
            return None, None
        
        # 查找序列文件和注释文件
        fna_file = genome_dir / f"{genome_id}_genomic.fna.gz"
        gff_file = genome_dir / f"{genome_id}_genomic.gff.gz"
        
        # 如果没有gff文件，尝试gtf文件
        if not gff_file.exists():
            gtf_file = genome_dir / f"{genome_id}_genomic.gtf.gz"
            if gtf_file.exists():
                gff_file = gtf_file
        
        return fna_file if fna_file.exists() else None, gff_file if gff_file.exists() else None
    
    def parse_gff_annotations(self, gff_file):
        """解析GFF/GTF注释文件，提取保守基因信息"""
        conserved_features = []
        
        try:
            with gzip.open(gff_file, 'rt', encoding='utf-8') as f:
                for line in f:
                    if line.startswith('#'):
                        continue
                    
                    parts = line.strip().split('\t')
                    if len(parts) < 9:
                        continue
                    
                    seqname, source, feature, start, end, score, strand, frame, attributes = parts
                    
                    # 检查是否是保守基因
                    for gene_type, keywords in self.conserved_genes.items():
                        for keyword in keywords:
                            if keyword.lower() in attributes.lower():
                                conserved_features.append({
                                    'gene_type': gene_type,
                                    'seqname': seqname,
                                    'start': int(start),
                                    'end': int(end),
                                    'strand': strand,
                                    'attributes': attributes
                                })
                                break
                        if conserved_features and conserved_features[-1]['gene_type'] == gene_type:
                            break
        
        except Exception as e:
            self.logger.error(f"解析GFF文件失败 {gff_file}: {e}")
        
        return conserved_features
    
    def extract_sequences_from_fasta(self, fna_file, features):
        """从FASTA文件中提取指定区域的序列"""
        extracted_sequences = []
        
        try:
            # 读取所有序列到内存
            sequences = {}
            with gzip.open(fna_file, 'rt', encoding='utf-8') as f:
                for record in SeqIO.parse(f, 'fasta'):
                    sequences[record.id] = record.seq
            
            # 提取保守序列
            for feature in features:
                seq_id = feature['seqname']
                if seq_id in sequences:
                    start = feature['start'] - 1  # 转换为0基坐标
                    end = feature['end']
                    strand = feature['strand']
                    
                    # 提取序列
                    seq = sequences[seq_id][start:end]
                    
                    # 如果是负链，取反向互补
                    if strand == '-':
                        seq = seq.reverse_complement()
                    
                    # 创建序列记录
                    seq_record = SeqRecord(
                        seq,
                        id=f"{seq_id}_{feature['gene_type']}_{start+1}_{end}",
                        description=f"{feature['gene_type']} from {seq_id} ({start+1}-{end}, {strand})"
                    )
                    
                    extracted_sequences.append({
                        'gene_type': feature['gene_type'],
                        'sequence': seq_record
                    })
        
        except Exception as e:
            self.logger.error(f"提取序列失败 {fna_file}: {e}")
        
        return extracted_sequences
    
    def extract_conserved_by_pattern(self, fna_file):
        """通过序列模式匹配提取保守序列（当没有注释文件时）"""
        extracted_sequences = []
        
        try:
            with gzip.open(fna_file, 'rt', encoding='utf-8') as f:
                for record in SeqIO.parse(f, 'fasta'):
                    seq_str = str(record.seq).upper()
                    
                    # 16S rRNA的保守区域模式（简化版）
                    # 这些是16S rRNA的高度保守区域
                    patterns = {
                        '16S_rRNA_V1': r'AGAGTTTGATC[ATCG]TGG[ATCG]TCAG',
                        '16S_rRNA_V3': r'CCTACGGG[ATCG][ATCG]GC[AT]GCAG',
                        '16S_rRNA_V4': r'GTGCCAGC[ATCG]GCCGCGGTAA'
                    }
                    
                    for pattern_name, pattern in patterns.items():
                        matches = re.finditer(pattern, seq_str)
                        for match in matches:
                            start, end = match.span()
                            # 扩展序列长度以获得更多上下文
                            extended_start = max(0, start - 50)
                            extended_end = min(len(seq_str), end + 50)
                            
                            conserved_seq = record.seq[extended_start:extended_end]
                            
                            seq_record = SeqRecord(
                                conserved_seq,
                                id=f"{record.id}_{pattern_name}_{extended_start+1}_{extended_end}",
                                description=f"{pattern_name} conserved region from {record.id}"
                            )
                            
                            extracted_sequences.append({
                                'gene_type': pattern_name,
                                'sequence': seq_record
                            })
        
        except Exception as e:
            self.logger.error(f"模式匹配提取失败 {fna_file}: {e}")
        
        return extracted_sequences
    
    def process_genome(self, genome_id, kingdom):
        """处理单个基因组"""
        self.logger.info(f"处理基因组: {genome_id} ({kingdom})")
        
        # 查找文件
        fna_file, gff_file = self.find_genome_files(genome_id, kingdom)
        
        if not fna_file:
            self.logger.warning(f"未找到序列文件: {genome_id}")
            return []
        
        extracted_sequences = []
        
        # 如果有注释文件，使用注释信息提取
        if gff_file:
            self.logger.info(f"使用注释文件提取: {gff_file}")
            features = self.parse_gff_annotations(gff_file)
            if features:
                extracted_sequences = self.extract_sequences_from_fasta(fna_file, features)
        
        # 如果没有注释文件或注释提取失败，使用模式匹配
        if not extracted_sequences:
            self.logger.info(f"使用模式匹配提取: {fna_file}")
            extracted_sequences = self.extract_conserved_by_pattern(fna_file)
        
        # 更新统计信息
        for seq_info in extracted_sequences:
            gene_type = seq_info['gene_type']
            if gene_type not in self.stats['found_sequences']:
                self.stats['found_sequences'][gene_type] = 0
            self.stats['found_sequences'][gene_type] += 1
        
        return extracted_sequences
    
    def save_sequences(self, all_sequences):
        """保存提取的序列"""
        # 按基因类型分组保存
        gene_groups = {}
        for genome_id, sequences in all_sequences.items():
            for seq_info in sequences:
                gene_type = seq_info['gene_type']
                if gene_type not in gene_groups:
                    gene_groups[gene_type] = []
                
                # 添加基因组ID到序列描述
                seq_record = seq_info['sequence']
                seq_record.description = f"{genome_id}|{seq_record.description}"
                gene_groups[gene_type].append(seq_record)
        
        # 保存每种基因类型的序列
        for gene_type, sequences in gene_groups.items():
            output_file = self.output_dir / f"{gene_type}_sequences.fasta"
            with open(output_file, 'w') as f:
                SeqIO.write(sequences, f, 'fasta')
            
            self.logger.info(f"保存 {len(sequences)} 个 {gene_type} 序列到 {output_file}")
        
        # 保存所有序列到一个文件
        all_seqs = []
        for sequences in gene_groups.values():
            all_seqs.extend(sequences)
        
        if all_seqs:
            all_output_file = self.output_dir / "all_conserved_sequences.fasta"
            with open(all_output_file, 'w') as f:
                SeqIO.write(all_seqs, f, 'fasta')
            
            self.logger.info(f"保存所有 {len(all_seqs)} 个保守序列到 {all_output_file}")
    
    def generate_report(self, all_sequences):
        """生成提取报告"""
        report_file = self.output_dir / "extraction_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("保守序列提取报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总基因组数: {self.stats['total_genomes']}\n")
            f.write(f"成功处理: {self.stats['processed_genomes']}\n")
            f.write(f"成功率: {(self.stats['processed_genomes']/max(self.stats['total_genomes'], 1)*100):.1f}%\n\n")
            
            f.write("提取的保守序列统计:\n")
            f.write("-" * 30 + "\n")
            for gene_type, count in self.stats['found_sequences'].items():
                f.write(f"{gene_type}: {count} 个序列\n")
            
            f.write(f"\n每个基因组的序列数量:\n")
            f.write("-" * 30 + "\n")
            for genome_id, sequences in all_sequences.items():
                f.write(f"{genome_id}: {len(sequences)} 个序列\n")
            
            if self.stats['errors']:
                f.write(f"\n错误信息:\n")
                f.write("-" * 30 + "\n")
                for error in self.stats['errors']:
                    f.write(f"{error}\n")
        
        self.logger.info(f"生成报告: {report_file}")
    
    def process_metadata_file(self, metadata_file, max_genomes=None):
        """处理metadata文件"""
        self.logger.info(f"开始处理metadata文件: {metadata_file}")
        
        # 读取metadata文件
        try:
            df = pd.read_csv(metadata_file, sep='\t')
            self.logger.info(f"读取到 {len(df)} 个基因组")
        except Exception as e:
            self.logger.error(f"读取metadata文件失败: {e}")
            return
        
        # 限制处理数量
        if max_genomes:
            df = df.head(max_genomes)
            self.logger.info(f"限制处理前 {max_genomes} 个基因组")
        
        self.stats['total_genomes'] = len(df)
        all_sequences = {}
        
        # 处理每个基因组
        for idx, row in df.iterrows():
            genome_id = row['genome_id']
            kingdom = row['kingdom']
            
            try:
                sequences = self.process_genome(genome_id, kingdom)
                if sequences:
                    all_sequences[genome_id] = sequences
                    self.stats['processed_genomes'] += 1
                
            except Exception as e:
                error_msg = f"处理基因组 {genome_id} 时出错: {e}"
                self.logger.error(error_msg)
                self.stats['errors'].append(error_msg)
        
        # 保存结果
        if all_sequences:
            self.save_sequences(all_sequences)
            self.generate_report(all_sequences)
        
        self.logger.info("处理完成!")

def main():
    parser = argparse.ArgumentParser(description='提取基因组保守序列')
    parser.add_argument('metadata_file', help='metadata-v2.tsv文件路径')
    parser.add_argument('--base-dir', default='data_downloads', help='基因组数据目录')
    parser.add_argument('--output-dir', default='conserved_sequences', help='输出目录')
    parser.add_argument('--max-genomes', type=int, help='最大处理基因组数量（用于测试）')
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.metadata_file):
        print(f"错误: metadata文件不存在: {args.metadata_file}")
        return
    
    # 创建提取器
    extractor = ConservedSequenceExtractor(
        base_dir=args.base_dir,
        output_dir=args.output_dir
    )
    
    # 开始处理
    extractor.process_metadata_file(args.metadata_file, args.max_genomes)

if __name__ == "__main__":
    main()
