#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的基因组特征提取器
从data_downloads目录下的基因序列和NCBI注释文件提取CDS、tRNA、rRNA特征

主要功能：
1. 从GFF/GTF注释文件解析基因信息
2. 提取CDS、tRNA、rRNA序列
3. 计算温度适应性相关特征
4. 支持多线程处理
5. 生成详细的特征报告

作者: AI Assistant
日期: 2025-07-14
"""

import os
import sys
import gzip
import json
import argparse
import pandas as pd
import numpy as np
import logging
import threading
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from collections import Counter, defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

# 尝试导入BioPython
try:
    from Bio import SeqIO
    from Bio.Seq import Seq
    from Bio.SeqRecord import SeqRecord
    from Bio.SeqUtils.ProtParam import ProteinAnalysis
    from Bio.Data import CodonTable
    try:
        from Bio.SeqUtils import gc_fraction as GC
    except ImportError:
        from Bio.SeqUtils import GC
    BIOPYTHON_AVAILABLE = True
except ImportError:
    BIOPYTHON_AVAILABLE = False
    print("警告: BioPython未安装，某些功能将不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GenomeAnnotationParser:
    """基因组注释文件解析器"""
    
    def __init__(self):
        self.features = {
            'CDS': [],
            'tRNA': [],
            'rRNA': [],
            'gene': []
        }
    
    def parse_gff_file(self, gff_file: str) -> Dict[str, List[Dict]]:
        """解析GFF/GTF注释文件"""
        logger.info(f"解析注释文件: {gff_file}")
        
        features = {
            'CDS': [],
            'tRNA': [],
            'rRNA': [],
            'gene': []
        }
        
        try:
            # 处理压缩文件
            if gff_file.endswith('.gz'):
                file_handle = gzip.open(gff_file, 'rt', encoding='utf-8')
            else:
                file_handle = open(gff_file, 'r', encoding='utf-8')
            
            with file_handle as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过注释行和空行
                    if line.startswith('#') or not line:
                        continue
                    
                    try:
                        parts = line.split('\t')
                        if len(parts) < 9:
                            continue
                        
                        seqname, source, feature_type, start, end, score, strand, frame, attributes = parts
                        
                        # 解析属性
                        attr_dict = self._parse_attributes(attributes)
                        
                        # 创建特征字典
                        feature_info = {
                            'seqname': seqname,
                            'source': source,
                            'type': feature_type,
                            'start': int(start),
                            'end': int(end),
                            'score': score,
                            'strand': strand,
                            'frame': frame,
                            'attributes': attr_dict,
                            'raw_attributes': attributes
                        }
                        
                        # 分类存储特征
                        if feature_type == 'CDS':
                            features['CDS'].append(feature_info)
                        elif feature_type in ['tRNA', 'transfer_RNA']:
                            features['tRNA'].append(feature_info)
                        elif feature_type in ['rRNA', 'ribosomal_RNA', '16S_rRNA', '23S_rRNA', '5S_rRNA']:
                            features['rRNA'].append(feature_info)
                        elif feature_type == 'gene':
                            features['gene'].append(feature_info)
                        
                        # 检查属性中的RNA类型
                        if 'product' in attr_dict:
                            product = attr_dict['product'].lower()
                            if 'trna' in product or 'transfer rna' in product:
                                if feature_info not in features['tRNA']:
                                    features['tRNA'].append(feature_info)
                            elif any(rna in product for rna in ['rrna', 'ribosomal rna', '16s', '23s', '5s']):
                                if feature_info not in features['rRNA']:
                                    features['rRNA'].append(feature_info)
                    
                    except Exception as e:
                        logger.warning(f"解析第{line_num}行失败: {e}")
                        continue
            
            logger.info(f"解析完成: CDS={len(features['CDS'])}, tRNA={len(features['tRNA'])}, rRNA={len(features['rRNA'])}, gene={len(features['gene'])}")
            
        except Exception as e:
            logger.error(f"解析注释文件失败: {e}")
        
        return features
    
    def _parse_attributes(self, attributes: str) -> Dict[str, str]:
        """解析GFF属性字段"""
        attr_dict = {}
        
        try:
            # 处理不同的属性格式
            if '=' in attributes:
                # GFF3格式: key=value;key2=value2
                for attr in attributes.split(';'):
                    if '=' in attr:
                        key, value = attr.split('=', 1)
                        attr_dict[key.strip()] = value.strip().strip('"')
            else:
                # GTF格式: key "value"; key2 "value2";
                import re
                pattern = r'(\w+)\s+"([^"]*)"'
                matches = re.findall(pattern, attributes)
                for key, value in matches:
                    attr_dict[key] = value
        
        except Exception as e:
            logger.warning(f"解析属性失败: {e}")
        
        return attr_dict

class SequenceExtractor:
    """序列提取器"""
    
    def __init__(self):
        pass
    
    def extract_sequences_from_genome(self, genome_file: str, features: Dict[str, List[Dict]]) -> Dict[str, List[SeqRecord]]:
        """从基因组文件中提取指定特征的序列"""
        logger.info(f"从基因组文件提取序列: {genome_file}")
        
        extracted_sequences = {
            'CDS': [],
            'tRNA': [],
            'rRNA': []
        }
        
        try:
            # 读取基因组序列
            genome_sequences = {}
            
            # 处理压缩文件
            if genome_file.endswith('.gz'):
                file_handle = gzip.open(genome_file, 'rt', encoding='utf-8')
            else:
                file_handle = open(genome_file, 'r', encoding='utf-8')
            
            with file_handle as f:
                if BIOPYTHON_AVAILABLE:
                    for record in SeqIO.parse(f, 'fasta'):
                        genome_sequences[record.id] = record.seq
                else:
                    # 简单的FASTA解析
                    current_id = None
                    current_seq = ""
                    
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_id and current_seq:
                                genome_sequences[current_id] = current_seq
                            current_id = line[1:].split()[0]  # 取第一个空格前的部分作为ID
                            current_seq = ""
                        else:
                            current_seq += line
                    
                    if current_id and current_seq:
                        genome_sequences[current_id] = current_seq
            
            logger.info(f"读取到 {len(genome_sequences)} 个序列片段")
            
            # 提取各类型序列
            for feature_type in ['CDS', 'tRNA', 'rRNA']:
                if feature_type in features:
                    for feature in features[feature_type]:
                        seq_record = self._extract_feature_sequence(
                            feature, genome_sequences, feature_type
                        )
                        if seq_record:
                            extracted_sequences[feature_type].append(seq_record)
            
            logger.info(f"提取序列完成: CDS={len(extracted_sequences['CDS'])}, "
                       f"tRNA={len(extracted_sequences['tRNA'])}, rRNA={len(extracted_sequences['rRNA'])}")
        
        except Exception as e:
            logger.error(f"序列提取失败: {e}")
        
        return extracted_sequences
    
    def _extract_feature_sequence(self, feature: Dict, genome_sequences: Dict, feature_type: str) -> Optional[SeqRecord]:
        """提取单个特征的序列"""
        try:
            seqname = feature['seqname']
            start = feature['start'] - 1  # 转换为0-based坐标
            end = feature['end']
            strand = feature['strand']
            
            # 查找匹配的序列
            target_seq = None
            for seq_id, seq in genome_sequences.items():
                if seqname in seq_id or seq_id in seqname:
                    target_seq = seq
                    break
            
            if target_seq is None:
                # 尝试精确匹配
                if seqname in genome_sequences:
                    target_seq = genome_sequences[seqname]
                else:
                    logger.warning(f"未找到序列: {seqname}")
                    return None
            
            # 提取序列片段
            if isinstance(target_seq, str):
                extracted_seq = target_seq[start:end]
                if strand == '-':
                    # 反向互补
                    extracted_seq = self._reverse_complement(extracted_seq)
                seq_obj = Seq(extracted_seq) if BIOPYTHON_AVAILABLE else extracted_seq
            else:
                # BioPython Seq对象
                extracted_seq = target_seq[start:end]
                if strand == '-':
                    extracted_seq = extracted_seq.reverse_complement()
                seq_obj = extracted_seq
            
            # 创建SeqRecord
            seq_id = f"{seqname}_{feature_type}_{start+1}_{end}_{strand}"
            description = f"{feature_type} from {seqname} ({start+1}-{end}, {strand})"
            
            if 'product' in feature['attributes']:
                description += f" | {feature['attributes']['product']}"
            
            if BIOPYTHON_AVAILABLE:
                seq_record = SeqRecord(seq_obj, id=seq_id, description=description)
            else:
                # 简单的序列记录
                seq_record = {
                    'id': seq_id,
                    'seq': seq_obj,
                    'description': description
                }
            
            return seq_record
        
        except Exception as e:
            logger.warning(f"提取特征序列失败: {e}")
            return None
    
    def _reverse_complement(self, seq: str) -> str:
        """计算反向互补序列"""
        complement = {'A': 'T', 'T': 'A', 'G': 'C', 'C': 'G', 'N': 'N'}
        return ''.join(complement.get(base, base) for base in reversed(seq.upper()))

class GenomeFeatureExtractor:
    """基因组特征提取器"""
    
    def __init__(self, genetic_code=11):
        self.genetic_code = genetic_code
        if BIOPYTHON_AVAILABLE:
            try:
                self.codon_table = CodonTable.unambiguous_dna_by_id[genetic_code]
            except:
                self.codon_table = None
        else:
            self.codon_table = None
        
        self.amino_acids = "ACDEFGHIKLMNPQRSTVWY"
        
        # 氨基酸理化性质
        self.aa_properties = {
            'A': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 89.1},
            'C': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 121.0},
            'D': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'mw': 133.1},
            'E': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': -1, 'mw': 147.1},
            'F': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 165.2},
            'G': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'mw': 75.1},
            'H': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 155.2},
            'I': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 131.2},
            'K': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 146.2},
            'L': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 131.2},
            'M': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 149.2},
            'N': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 132.1},
            'P': {'hydrophobic': False, 'polar': False, 'charged': False, 'charge': 0, 'mw': 115.1},
            'Q': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 146.2},
            'R': {'hydrophobic': False, 'polar': True, 'charged': True, 'charge': 1, 'mw': 174.2},
            'S': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 105.1},
            'T': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 119.1},
            'V': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 117.1},
            'W': {'hydrophobic': True, 'polar': False, 'charged': False, 'charge': 0, 'mw': 204.2},
            'Y': {'hydrophobic': False, 'polar': True, 'charged': False, 'charge': 0, 'mw': 181.2}
        }
    
    def extract_all_features(self, genome_file: str, annotation_file: str) -> Dict[str, float]:
        """提取所有特征"""
        logger.info(f"开始提取基因组特征")
        
        features = {}
        
        try:
            # 1. 解析注释文件
            parser = GenomeAnnotationParser()
            annotations = parser.parse_gff_file(annotation_file)
            
            # 2. 提取序列
            extractor = SequenceExtractor()
            sequences = extractor.extract_sequences_from_genome(genome_file, annotations)
            
            # 3. 基因组基本特征
            genome_features = self._extract_genome_basic_features(genome_file)
            features.update(genome_features)
            
            # 4. CDS特征
            if sequences['CDS']:
                cds_features = self._extract_cds_features(sequences['CDS'])
                features.update(cds_features)
            
            # 5. tRNA特征
            if sequences['tRNA']:
                trna_features = self._extract_trna_features(sequences['tRNA'])
                features.update(trna_features)
            
            # 6. rRNA特征
            if sequences['rRNA']:
                rrna_features = self._extract_rrna_features(sequences['rRNA'])
                features.update(rrna_features)
            
            # 7. 基因组结构特征
            structure_features = self._extract_structure_features(annotations)
            features.update(structure_features)
            
            logger.info(f"特征提取完成，共 {len(features)} 个特征")
            
        except Exception as e:
            logger.error(f"特征提取失败: {e}")
        
        return features

    def _extract_genome_basic_features(self, genome_file: str) -> Dict[str, float]:
        """提取基因组基本特征"""
        features = {}

        try:
            # 处理压缩文件
            if genome_file.endswith('.gz'):
                file_handle = gzip.open(genome_file, 'rt', encoding='utf-8')
            else:
                file_handle = open(genome_file, 'r', encoding='utf-8')

            sequences = []
            total_length = 0

            with file_handle as f:
                if BIOPYTHON_AVAILABLE:
                    for record in SeqIO.parse(f, 'fasta'):
                        seq_str = str(record.seq).upper()
                        sequences.append(seq_str)
                        total_length += len(seq_str)
                else:
                    current_seq = ""
                    for line in f:
                        line = line.strip()
                        if line.startswith('>'):
                            if current_seq:
                                sequences.append(current_seq.upper())
                                total_length += len(current_seq)
                                current_seq = ""
                        else:
                            current_seq += line
                    if current_seq:
                        sequences.append(current_seq.upper())
                        total_length += len(current_seq)

            if not sequences:
                return features

            # 合并所有序列
            genome_seq = ''.join(sequences)

            # 基本统计
            features['genome_size'] = total_length
            features['num_contigs'] = len(sequences)
            features['avg_contig_length'] = total_length / len(sequences) if sequences else 0

            # 核苷酸组成
            a_count = genome_seq.count('A')
            t_count = genome_seq.count('T')
            g_count = genome_seq.count('G')
            c_count = genome_seq.count('C')

            features['a_content'] = a_count / total_length if total_length > 0 else 0
            features['t_content'] = t_count / total_length if total_length > 0 else 0
            features['g_content'] = g_count / total_length if total_length > 0 else 0
            features['c_content'] = c_count / total_length if total_length > 0 else 0

            # GC含量
            features['gc_content'] = (g_count + c_count) / total_length if total_length > 0 else 0
            features['at_content'] = (a_count + t_count) / total_length if total_length > 0 else 0

            # 核苷酸偏斜
            features['gc_skew'] = (g_count - c_count) / (g_count + c_count) if (g_count + c_count) > 0 else 0
            features['at_skew'] = (a_count - t_count) / (a_count + t_count) if (a_count + t_count) > 0 else 0

            # 二核苷酸频率
            dinucleotides = ['AA', 'AT', 'AG', 'AC', 'TA', 'TT', 'TG', 'TC',
                           'GA', 'GT', 'GG', 'GC', 'CA', 'CT', 'CG', 'CC']

            for dinuc in dinucleotides:
                count = 0
                for i in range(len(genome_seq) - 1):
                    if genome_seq[i:i+2] == dinuc:
                        count += 1
                features[f'dinuc_{dinuc.lower()}'] = count / (total_length - 1) if total_length > 1 else 0

            # CpG观察/期望比值
            cg_observed = genome_seq.count('CG')
            cg_expected = (c_count * g_count) / total_length if total_length > 0 else 0
            features['cpg_oe_ratio'] = cg_observed / cg_expected if cg_expected > 0 else 0

        except Exception as e:
            logger.error(f"基因组基本特征提取失败: {e}")

        return features

    def _extract_cds_features(self, cds_sequences: List) -> Dict[str, float]:
        """提取CDS特征"""
        features = {}

        try:
            # 合并所有CDS序列
            all_cds = ""
            cds_lengths = []

            for seq_record in cds_sequences:
                if BIOPYTHON_AVAILABLE and hasattr(seq_record, 'seq'):
                    seq_str = str(seq_record.seq).upper()
                else:
                    seq_str = seq_record['seq'].upper() if isinstance(seq_record, dict) else str(seq_record).upper()

                # 确保序列长度是3的倍数
                if len(seq_str) % 3 == 0:
                    all_cds += seq_str
                    cds_lengths.append(len(seq_str))

            if not all_cds:
                return features

            # CDS基本统计
            features['total_cds_count'] = len(cds_sequences)
            features['total_cds_length'] = len(all_cds)
            features['avg_cds_length'] = np.mean(cds_lengths) if cds_lengths else 0
            features['median_cds_length'] = np.median(cds_lengths) if cds_lengths else 0
            features['std_cds_length'] = np.std(cds_lengths) if cds_lengths else 0

            # CDS的GC含量
            cds_gc = (all_cds.count('G') + all_cds.count('C')) / len(all_cds) if all_cds else 0
            features['cds_gc_content'] = cds_gc

            # 密码子使用特征
            if self.codon_table:
                codon_features = self._extract_codon_usage_features(all_cds)
                features.update(codon_features)

            # 氨基酸组成特征
            aa_features = self._extract_amino_acid_features(all_cds)
            features.update(aa_features)

        except Exception as e:
            logger.error(f"CDS特征提取失败: {e}")

        return features

    def _extract_codon_usage_features(self, cds_sequence: str) -> Dict[str, float]:
        """提取密码子使用特征"""
        features = {}

        try:
            # 统计密码子
            codon_counts = defaultdict(int)
            aa_counts = defaultdict(int)

            for i in range(0, len(cds_sequence), 3):
                codon = cds_sequence[i:i+3]
                if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                    codon_counts[codon] += 1
                    if codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        aa_counts[aa] += 1

            total_codons = sum(codon_counts.values())

            # 密码子频率
            for codon in self.codon_table.forward_table:
                features[f'codon_{codon.lower()}'] = codon_counts[codon] / total_codons if total_codons > 0 else 0

            # 有效密码子数 (ENC)
            features['enc'] = self._calculate_enc(codon_counts, aa_counts)

            # 密码子使用偏好
            features['cub'] = self._calculate_cub(codon_counts, aa_counts)

            # 密码子位置的GC含量
            gc1 = gc2 = gc3 = 0
            total_positions = 0

            for i in range(0, len(cds_sequence), 3):
                codon = cds_sequence[i:i+3]
                if len(codon) == 3:
                    if codon[0] in 'GC': gc1 += 1
                    if codon[1] in 'GC': gc2 += 1
                    if codon[2] in 'GC': gc3 += 1
                    total_positions += 1

            features['gc1'] = gc1 / total_positions if total_positions > 0 else 0
            features['gc2'] = gc2 / total_positions if total_positions > 0 else 0
            features['gc3'] = gc3 / total_positions if total_positions > 0 else 0

        except Exception as e:
            logger.error(f"密码子使用特征提取失败: {e}")

        return features

    def _extract_amino_acid_features(self, cds_sequence: str) -> Dict[str, float]:
        """提取氨基酸组成特征"""
        features = {}

        try:
            # 翻译为氨基酸序列
            aa_sequence = ""
            for i in range(0, len(cds_sequence), 3):
                codon = cds_sequence[i:i+3]
                if len(codon) == 3 and all(base in 'ATGC' for base in codon):
                    if self.codon_table and codon in self.codon_table.forward_table:
                        aa = self.codon_table.forward_table[codon]
                        aa_sequence += aa

            if not aa_sequence:
                return features

            # 氨基酸组成
            aa_counts = Counter(aa_sequence)
            total_aa = len(aa_sequence)

            for aa in self.amino_acids:
                features[f'aa_{aa.lower()}'] = aa_counts[aa] / total_aa if total_aa > 0 else 0

            # 氨基酸理化性质
            hydrophobic_count = sum(1 for aa in aa_sequence if self.aa_properties.get(aa, {}).get('hydrophobic', False))
            polar_count = sum(1 for aa in aa_sequence if self.aa_properties.get(aa, {}).get('polar', False))
            charged_count = sum(1 for aa in aa_sequence if self.aa_properties.get(aa, {}).get('charged', False))

            features['hydrophobic_ratio'] = hydrophobic_count / total_aa if total_aa > 0 else 0
            features['polar_ratio'] = polar_count / total_aa if total_aa > 0 else 0
            features['charged_ratio'] = charged_count / total_aa if total_aa > 0 else 0

            # 平均分子量
            total_mw = sum(self.aa_properties.get(aa, {}).get('mw', 0) for aa in aa_sequence)
            features['avg_molecular_weight'] = total_mw / total_aa if total_aa > 0 else 0

            # 净电荷
            total_charge = sum(self.aa_properties.get(aa, {}).get('charge', 0) for aa in aa_sequence)
            features['net_charge'] = total_charge / total_aa if total_aa > 0 else 0

            # 芳香族氨基酸
            aromatic_count = sum(1 for aa in aa_sequence if aa in 'FWY')
            features['aromatic_ratio'] = aromatic_count / total_aa if total_aa > 0 else 0

            # 脂肪族氨基酸
            aliphatic_count = sum(1 for aa in aa_sequence if aa in 'AILV')
            features['aliphatic_ratio'] = aliphatic_count / total_aa if total_aa > 0 else 0

        except Exception as e:
            logger.error(f"氨基酸特征提取失败: {e}")

        return features

    def _extract_trna_features(self, trna_sequences: List) -> Dict[str, float]:
        """提取tRNA特征"""
        features = {}

        try:
            # tRNA基本统计
            features['total_trna_count'] = len(trna_sequences)

            trna_lengths = []
            trna_gc_contents = []

            for seq_record in trna_sequences:
                if BIOPYTHON_AVAILABLE and hasattr(seq_record, 'seq'):
                    seq_str = str(seq_record.seq).upper()
                else:
                    seq_str = seq_record['seq'].upper() if isinstance(seq_record, dict) else str(seq_record).upper()

                trna_lengths.append(len(seq_str))

                # GC含量
                gc_count = seq_str.count('G') + seq_str.count('C')
                gc_content = gc_count / len(seq_str) if len(seq_str) > 0 else 0
                trna_gc_contents.append(gc_content)

            if trna_lengths:
                features['avg_trna_length'] = np.mean(trna_lengths)
                features['median_trna_length'] = np.median(trna_lengths)
                features['std_trna_length'] = np.std(trna_lengths)

            if trna_gc_contents:
                features['avg_trna_gc'] = np.mean(trna_gc_contents)
                features['std_trna_gc'] = np.std(trna_gc_contents)

        except Exception as e:
            logger.error(f"tRNA特征提取失败: {e}")

        return features

    def _extract_rrna_features(self, rrna_sequences: List) -> Dict[str, float]:
        """提取rRNA特征"""
        features = {}

        try:
            # rRNA基本统计
            features['total_rrna_count'] = len(rrna_sequences)

            rrna_lengths = []
            rrna_gc_contents = []
            rrna_types = defaultdict(int)

            for seq_record in rrna_sequences:
                if BIOPYTHON_AVAILABLE and hasattr(seq_record, 'seq'):
                    seq_str = str(seq_record.seq).upper()
                    description = seq_record.description
                else:
                    seq_str = seq_record['seq'].upper() if isinstance(seq_record, dict) else str(seq_record).upper()
                    description = seq_record.get('description', '') if isinstance(seq_record, dict) else ''

                rrna_lengths.append(len(seq_str))

                # GC含量
                gc_count = seq_str.count('G') + seq_str.count('C')
                gc_content = gc_count / len(seq_str) if len(seq_str) > 0 else 0
                rrna_gc_contents.append(gc_content)

                # rRNA类型识别
                description_lower = description.lower()
                if '16s' in description_lower:
                    rrna_types['16S'] += 1
                elif '23s' in description_lower:
                    rrna_types['23S'] += 1
                elif '5s' in description_lower:
                    rrna_types['5S'] += 1
                else:
                    rrna_types['other'] += 1

            if rrna_lengths:
                features['avg_rrna_length'] = np.mean(rrna_lengths)
                features['median_rrna_length'] = np.median(rrna_lengths)
                features['std_rrna_length'] = np.std(rrna_lengths)

            if rrna_gc_contents:
                features['avg_rrna_gc'] = np.mean(rrna_gc_contents)
                features['std_rrna_gc'] = np.std(rrna_gc_contents)

            # rRNA类型计数
            for rna_type, count in rrna_types.items():
                features[f'rrna_{rna_type.lower()}_count'] = count

        except Exception as e:
            logger.error(f"rRNA特征提取失败: {e}")

        return features

    def _extract_structure_features(self, annotations: Dict[str, List[Dict]]) -> Dict[str, float]:
        """提取基因组结构特征"""
        features = {}

        try:
            # 基因密度
            total_genes = len(annotations.get('gene', []))
            total_cds = len(annotations.get('CDS', []))
            total_trna = len(annotations.get('tRNA', []))
            total_rrna = len(annotations.get('rRNA', []))

            features['gene_density'] = total_genes
            features['cds_density'] = total_cds
            features['trna_density'] = total_trna
            features['rrna_density'] = total_rrna

            # 基因间距分析
            if annotations.get('gene'):
                gene_positions = [(gene['start'], gene['end']) for gene in annotations['gene']]
                gene_positions.sort()

                intergenic_distances = []
                for i in range(len(gene_positions) - 1):
                    distance = gene_positions[i+1][0] - gene_positions[i][1]
                    if distance > 0:
                        intergenic_distances.append(distance)

                if intergenic_distances:
                    features['avg_intergenic_distance'] = np.mean(intergenic_distances)
                    features['median_intergenic_distance'] = np.median(intergenic_distances)
                    features['std_intergenic_distance'] = np.std(intergenic_distances)

            # 链偏好
            plus_strand_genes = sum(1 for gene in annotations.get('gene', []) if gene.get('strand') == '+')
            minus_strand_genes = sum(1 for gene in annotations.get('gene', []) if gene.get('strand') == '-')

            total_strand_genes = plus_strand_genes + minus_strand_genes
            if total_strand_genes > 0:
                features['plus_strand_ratio'] = plus_strand_genes / total_strand_genes
                features['minus_strand_ratio'] = minus_strand_genes / total_strand_genes
                features['strand_bias'] = abs(plus_strand_genes - minus_strand_genes) / total_strand_genes

        except Exception as e:
            logger.error(f"结构特征提取失败: {e}")

        return features

    def _calculate_enc(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算有效密码子数 (Effective Number of Codons)"""
        try:
            if not aa_counts:
                return 0.0

            # 简化的ENC计算
            total_codons = sum(codon_counts.values())
            if total_codons == 0:
                return 0.0

            # 计算每个氨基酸的有效密码子数
            aa_enc_values = []

            for aa, count in aa_counts.items():
                if count > 0:
                    # 找到编码该氨基酸的所有密码子
                    aa_codons = [codon for codon, amino_acid in self.codon_table.forward_table.items() if amino_acid == aa]

                    if len(aa_codons) > 1:
                        # 计算该氨基酸的密码子使用方差
                        aa_codon_freqs = [codon_counts[codon] / count for codon in aa_codons]
                        variance = np.var(aa_codon_freqs) if len(aa_codon_freqs) > 1 else 0

                        # 有效密码子数
                        if variance > 0:
                            enc_aa = 1 / (1 + variance * (len(aa_codons) - 1))
                        else:
                            enc_aa = len(aa_codons)
                    else:
                        enc_aa = 1

                    aa_enc_values.append(enc_aa)

            return np.mean(aa_enc_values) if aa_enc_values else 0.0

        except Exception as e:
            logger.error(f"ENC计算失败: {e}")
            return 0.0

    def _calculate_cub(self, codon_counts: Dict[str, int], aa_counts: Dict[str, int]) -> float:
        """计算密码子使用偏好 (Codon Usage Bias)"""
        try:
            if not codon_counts:
                return 0.0

            total_codons = sum(codon_counts.values())
            if total_codons == 0:
                return 0.0

            # 计算期望频率（均匀分布）
            expected_freq = 1.0 / len(self.codon_table.forward_table)

            # 计算观察频率与期望频率的偏差
            chi_square = 0.0
            for codon, count in codon_counts.items():
                observed_freq = count / total_codons
                chi_square += (observed_freq - expected_freq) ** 2 / expected_freq

            return chi_square

        except Exception as e:
            logger.error(f"CUB计算失败: {e}")
            return 0.0

class GenomeProcessor:
    """基因组处理器 - 支持多线程处理"""

    def __init__(self, base_dir: str = "data_downloads", max_workers: int = 4):
        self.base_dir = Path(base_dir)
        self.max_workers = max_workers
        self.thread_lock = threading.Lock()

        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'processed_genomes': 0,
            'failed_genomes': 0,
            'errors': []
        }

    def find_genome_files(self, genome_id: str) -> Tuple[Optional[str], Optional[str]]:
        """查找基因组文件和注释文件"""
        # 在各个子目录中搜索
        for subdir in ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']:
            genome_dir = self.base_dir / subdir / genome_id

            if genome_dir.exists():
                # 查找基因组序列文件
                fna_file = genome_dir / f"{genome_id}_genomic.fna.gz"
                if not fna_file.exists():
                    fna_file = genome_dir / f"{genome_id}_genomic.fna"

                # 查找注释文件
                gff_file = genome_dir / f"{genome_id}_genomic.gff.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gff"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf"

                return (str(fna_file) if fna_file.exists() else None,
                       str(gff_file) if gff_file.exists() else None)

        return None, None

    def process_single_genome(self, genome_data: Tuple[int, pd.Series]) -> Dict:
        """处理单个基因组"""
        idx, row = genome_data
        genome_id = row['genome_id']

        try:
            thread_id = threading.current_thread().ident
            logger.info(f"[线程{thread_id}] 处理基因组: {genome_id}")

            # 查找文件
            genome_file, annotation_file = self.find_genome_files(genome_id)

            if not genome_file:
                return {
                    'genome_id': genome_id,
                    'status': 'failed',
                    'error': 'Genome file not found',
                    'features': {}
                }

            if not annotation_file:
                return {
                    'genome_id': genome_id,
                    'status': 'failed',
                    'error': 'Annotation file not found',
                    'features': {}
                }

            # 提取特征
            extractor = GenomeFeatureExtractor()
            features = extractor.extract_all_features(genome_file, annotation_file)

            # 添加元数据
            features['genome_id'] = genome_id
            if 'optimal_temperature' in row:
                features['optimal_temperature'] = row['optimal_temperature']
            if 'kingdom' in row:
                features['kingdom'] = row['kingdom']

            with self.thread_lock:
                self.stats['processed_genomes'] += 1

            return {
                'genome_id': genome_id,
                'status': 'success',
                'error': None,
                'features': features
            }

        except Exception as e:
            error_msg = f"处理基因组 {genome_id} 时出错: {e}"
            logger.error(f"[线程{thread_id}] {error_msg}")

            with self.thread_lock:
                self.stats['failed_genomes'] += 1
                self.stats['errors'].append(error_msg)

            return {
                'genome_id': genome_id,
                'status': 'failed',
                'error': str(e),
                'features': {}
            }

    def process_metadata_file(self, metadata_file: str, output_file: str = None,
                            max_genomes: int = None, use_threading: bool = True) -> str:
        """处理metadata文件，提取所有基因组的特征"""
        logger.info(f"开始处理metadata文件: {metadata_file}")

        # 读取metadata文件
        try:
            df = pd.read_csv(metadata_file, sep='\t')
            logger.info(f"读取到 {len(df)} 个基因组")
        except Exception as e:
            logger.error(f"读取metadata文件失败: {e}")
            return None

        # 限制处理数量
        if max_genomes:
            df = df.head(max_genomes)
            logger.info(f"限制处理前 {max_genomes} 个基因组")

        self.stats['total_genomes'] = len(df)

        # 处理数据
        all_features = []

        if use_threading and self.max_workers > 1:
            logger.info(f"使用多线程处理，工作线程数: {self.max_workers}")

            # 准备任务
            tasks = [(idx, row) for idx, row in df.iterrows()]

            # 使用线程池
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_task = {executor.submit(self.process_single_genome, task): task for task in tasks}

                for future in as_completed(future_to_task):
                    try:
                        result = future.result()
                        if result['status'] == 'success':
                            all_features.append(result['features'])

                        # 进度报告
                        completed = self.stats['processed_genomes'] + self.stats['failed_genomes']
                        if completed % 10 == 0:
                            progress = (completed / len(tasks)) * 100
                            logger.info(f"进度: {completed}/{len(tasks)} ({progress:.1f}%)")

                    except Exception as e:
                        logger.error(f"线程任务执行失败: {e}")

        else:
            logger.info("使用顺序处理模式")

            for idx, (_, row) in enumerate(df.iterrows()):
                result = self.process_single_genome((idx, row))
                if result['status'] == 'success':
                    all_features.append(result['features'])

                # 进度报告
                if (idx + 1) % 10 == 0:
                    progress = ((idx + 1) / len(df)) * 100
                    logger.info(f"进度: {idx + 1}/{len(df)} ({progress:.1f}%)")

        # 保存结果
        if all_features:
            features_df = pd.DataFrame(all_features)

            if output_file is None:
                output_file = f"genome_features_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            features_df.to_csv(output_file, index=False)
            logger.info(f"特征数据已保存到: {output_file}")

            # 生成统计报告
            self._generate_report(output_file.replace('.csv', '_report.txt'))

            return output_file
        else:
            logger.error("没有成功提取任何特征")
            return None

    def _generate_report(self, report_file: str):
        """生成处理报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("基因组特征提取报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总基因组数: {self.stats['total_genomes']}\n")
            f.write(f"成功处理: {self.stats['processed_genomes']}\n")
            f.write(f"失败数量: {self.stats['failed_genomes']}\n")
            f.write(f"成功率: {(self.stats['processed_genomes']/max(self.stats['total_genomes'], 1)*100):.1f}%\n\n")

            if self.stats['errors']:
                f.write("错误信息:\n")
                f.write("-" * 30 + "\n")
                for error in self.stats['errors'][:10]:  # 只显示前10个错误
                    f.write(f"{error}\n")
                if len(self.stats['errors']) > 10:
                    f.write(f"... 还有 {len(self.stats['errors']) - 10} 个错误\n")

        logger.info(f"生成处理报告: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='优化的基因组特征提取器')
    parser.add_argument('metadata_file', help='metadata-v2.tsv文件路径')
    parser.add_argument('--base-dir', default='data_downloads', help='基因组数据目录')
    parser.add_argument('--output-file', help='输出文件路径')
    parser.add_argument('--max-genomes', type=int, help='最大处理基因组数量（用于测试）')
    parser.add_argument('--max-workers', type=int, default=4, help='最大工作线程数')
    parser.add_argument('--no-threading', action='store_true', help='禁用多线程')

    args = parser.parse_args()

    # 检查BioPython
    if not BIOPYTHON_AVAILABLE:
        logger.warning("BioPython未安装，某些功能将受限")
        response = input("是否继续? [y/N]: ").strip().lower()
        if response not in ['y', 'yes']:
            sys.exit(1)

    # 检查输入文件
    if not os.path.exists(args.metadata_file):
        logger.error(f"输入文件不存在: {args.metadata_file}")
        sys.exit(1)

    # 创建处理器
    processor = GenomeProcessor(
        base_dir=args.base_dir,
        max_workers=args.max_workers
    )

    # 开始处理
    try:
        output_file = processor.process_metadata_file(
            args.metadata_file,
            output_file=args.output_file,
            max_genomes=args.max_genomes,
            use_threading=not args.no_threading
        )

        if output_file:
            logger.info(f"处理完成! 结果保存在: {output_file}")
        else:
            logger.error("处理失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断处理")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
