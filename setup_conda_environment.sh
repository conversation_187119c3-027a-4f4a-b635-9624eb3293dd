#!/bin/bash
# 设置conda环境用于基因组特征提取

echo "=== 基因组特征提取环境设置 ==="

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "错误: conda未安装或未在PATH中"
    echo "请先安装Anaconda或Miniconda"
    exit 1
fi

echo "✅ 检测到conda: $(conda --version)"

# 检查environment.yml文件
if [ ! -f "environment.yml" ]; then
    echo "错误: environment.yml文件不存在"
    exit 1
fi

echo "✅ 找到environment.yml文件"

# 创建conda环境
echo "📦 创建conda环境: genome_analysis"
conda env create -f environment.yml

# 检查环境是否创建成功
if [ $? -eq 0 ]; then
    echo "✅ 环境创建成功"
else
    echo "❌ 环境创建失败"
    exit 1
fi

# 激活环境并验证安装
echo "🔍 验证安装的包..."
conda activate genome_analysis

# 验证关键包
python -c "
import sys
print(f'Python版本: {sys.version}')

try:
    import Bio
    print(f'✅ BioPython: {Bio.__version__}')
except ImportError:
    print('❌ BioPython未安装')

try:
    import pandas as pd
    print(f'✅ Pandas: {pd.__version__}')
except ImportError:
    print('❌ Pandas未安装')

try:
    import numpy as np
    print(f'✅ NumPy: {np.__version__}')
except ImportError:
    print('❌ NumPy未安装')

try:
    import matplotlib
    print(f'✅ Matplotlib: {matplotlib.__version__}')
except ImportError:
    print('❌ Matplotlib未安装')

try:
    import sklearn
    print(f'✅ Scikit-learn: {sklearn.__version__}')
except ImportError:
    print('❌ Scikit-learn未安装')
"

echo ""
echo "🎉 环境设置完成!"
echo ""
echo "使用方法:"
echo "1. 激活环境: conda activate genome_analysis"
echo "2. 运行脚本: python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv"
echo "3. 退出环境: conda deactivate"
echo ""
echo "测试命令:"
echo "conda activate genome_analysis"
echo "python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-genomes 3"
