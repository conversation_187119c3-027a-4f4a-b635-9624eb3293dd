#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GO注释功能特征提取测试脚本

测试功能：
1. 测试GO注释解析
2. 测试GO特征提取
3. 测试集成特征提取
4. 验证断点恢复功能

作者: AI Assistant
日期: 2025-07-14
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import logging

# 导入自定义模块
from go_annotation_feature_extractor import GODatabase, GOAnnotationParser, GOFeatureExtractor, GOGenomeProcessor
from integrated_genome_feature_extractor import IntegratedGenomeFeatureExtractor

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_go_database():
    """测试GO数据库功能"""
    print("=== 测试GO数据库 ===")
    
    go_db = GODatabase(cache_dir="test_go_cache")
    
    # 测试基本功能
    print(f"GO术语数量: {len(go_db.go_terms)}")
    
    # 测试分类功能
    test_terms = ['GO:0003674', 'GO:0008150', 'GO:0005575']
    for term in test_terms:
        category = go_db.get_go_category(term)
        name = go_db.get_go_name(term)
        print(f"{term}: {name} ({category})")
    
    # 添加新术语测试
    go_db.add_go_term('GO:0000001', 'test_term', 'molecular_function')
    print(f"添加测试术语后: {len(go_db.go_terms)} 个术语")
    
    # 保存缓存测试
    go_db.save_cached_data()
    print("✅ GO数据库测试完成")

def test_go_annotation_parser():
    """测试GO注释解析器"""
    print("\n=== 测试GO注释解析器 ===")
    
    go_db = GODatabase(cache_dir="test_go_cache")
    parser = GOAnnotationParser(go_db)
    
    # 查找测试文件
    test_gff_file = None
    data_dir = Path("data_downloads")
    
    # 查找第一个可用的GFF文件
    for subdir in ['Bacteria', 'Archaea']:
        subdir_path = data_dir / subdir
        if subdir_path.exists():
            for genome_dir in subdir_path.iterdir():
                if genome_dir.is_dir():
                    gff_files = list(genome_dir.glob("*_genomic.gff*"))
                    if gff_files:
                        test_gff_file = str(gff_files[0])
                        break
            if test_gff_file:
                break
    
    if test_gff_file:
        print(f"测试文件: {test_gff_file}")
        
        # 解析GO注释
        go_annotations = parser.parse_gff_go_annotations(test_gff_file)
        
        gene_go_map = go_annotations.get('gene_go_map', {})
        go_gene_map = go_annotations.get('go_gene_map', {})
        
        print(f"注释基因数: {len(gene_go_map)}")
        print(f"GO术语数: {len(go_gene_map)}")
        
        # 显示一些示例
        if gene_go_map:
            sample_genes = list(gene_go_map.keys())[:3]
            for gene in sample_genes:
                go_terms = gene_go_map[gene]
                print(f"基因 {gene}: {len(go_terms)} 个GO术语")
                if go_terms:
                    print(f"  示例GO术语: {go_terms[:3]}")
        
        print("✅ GO注释解析器测试完成")
    else:
        print("❌ 未找到测试GFF文件")

def test_go_feature_extractor():
    """测试GO特征提取器"""
    print("\n=== 测试GO特征提取器 ===")
    
    go_db = GODatabase(cache_dir="test_go_cache")
    parser = GOAnnotationParser(go_db)
    extractor = GOFeatureExtractor(go_db)
    
    # 创建模拟GO注释数据
    mock_gene_go_map = {
        'gene1': ['GO:0003674', 'GO:0008150'],
        'gene2': ['GO:0005575', 'GO:0003674'],
        'gene3': ['GO:0008150'],
        'gene4': ['GO:0003674', 'GO:0005575', 'GO:0008150'],
        'gene5': []
    }
    
    mock_go_gene_map = {
        'GO:0003674': ['gene1', 'gene2', 'gene4'],
        'GO:0008150': ['gene1', 'gene3', 'gene4'],
        'GO:0005575': ['gene2', 'gene4']
    }
    
    mock_annotations = {
        'gene_go_map': mock_gene_go_map,
        'go_gene_map': mock_go_gene_map
    }
    
    # 提取特征
    features = extractor.extract_go_features(mock_annotations)
    
    print(f"提取的GO特征数量: {len(features)}")
    
    # 显示一些关键特征
    key_features = [
        'total_annotated_genes', 'total_go_terms', 'avg_go_per_gene',
        'molecular_function_go_count', 'biological_process_go_count', 'cellular_component_go_count',
        'go_shannon_diversity', 'go_simpson_diversity'
    ]
    
    print("关键特征值:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.4f}")
    
    print("✅ GO特征提取器测试完成")

def test_go_genome_processor():
    """测试GO基因组处理器"""
    print("\n=== 测试GO基因组处理器 ===")
    
    # 创建测试处理器
    processor = GOGenomeProcessor(
        base_dir="data_downloads",
        output_dir="test_go_output",
        max_workers=2,
        checkpoint_interval=5
    )
    
    # 创建测试metadata
    test_metadata = pd.DataFrame({
        'genome_id': ['GCA_000219855.1', 'GCA_000283575.1'],
        'optimal_temperature': [50.0, 30.0],
        'kingdom': ['Bacteria', 'Bacteria']
    })
    
    test_metadata_file = "test_metadata_go.tsv"
    test_metadata.to_csv(test_metadata_file, sep='\t', index=False)
    
    try:
        # 测试处理
        output_file = processor.process_metadata_file(
            test_metadata_file,
            output_file="test_go_features.csv",
            max_genomes=2,
            use_threading=True
        )
        
        if output_file and os.path.exists(output_file):
            # 检查结果
            df = pd.read_csv(output_file)
            print(f"成功处理: {len(df)} 个基因组")
            print(f"特征数量: {len(df.columns)}")
            
            # 显示GO特征统计
            go_features = [col for col in df.columns if col.startswith('go_')]
            print(f"GO特征数量: {len(go_features)}")
            
            # 显示一些统计
            if 'go_total_annotated_genes' in df.columns:
                annotated_genomes = (df['go_total_annotated_genes'] > 0).sum()
                print(f"有GO注释的基因组: {annotated_genomes}/{len(df)}")
            
            print("✅ GO基因组处理器测试完成")
        else:
            print("❌ GO基因组处理器测试失败")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_metadata_file):
            os.remove(test_metadata_file)

def test_integrated_feature_extractor():
    """测试集成特征提取器"""
    print("\n=== 测试集成特征提取器 ===")
    
    # 创建测试处理器
    processor = IntegratedGenomeFeatureExtractor(
        base_dir="data_downloads",
        output_dir="test_integrated_output",
        max_workers=2,
        checkpoint_interval=5
    )
    
    # 创建测试metadata
    test_metadata = pd.DataFrame({
        'genome_id': ['GCA_000219855.1', 'GCA_000283575.1'],
        'optimal_temperature': [50.0, 30.0],
        'kingdom': ['Bacteria', 'Bacteria']
    })
    
    test_metadata_file = "test_metadata_integrated.tsv"
    test_metadata.to_csv(test_metadata_file, sep='\t', index=False)
    
    try:
        # 测试处理
        output_file = processor.process_metadata_file(
            test_metadata_file,
            output_file="test_integrated_features.csv",
            max_genomes=2,
            use_threading=True
        )
        
        if output_file and os.path.exists(output_file):
            # 检查结果
            df = pd.read_csv(output_file)
            print(f"成功处理: {len(df)} 个基因组")
            print(f"总特征数量: {len(df.columns)}")
            
            # 分析特征类型
            basic_features = [col for col in df.columns if col.startswith('basic_')]
            go_features = [col for col in df.columns if col.startswith('go_')]
            
            print(f"基本特征数量: {len(basic_features)}")
            print(f"GO特征数量: {len(go_features)}")
            
            # 显示特征覆盖情况
            if basic_features:
                basic_coverage = (df[basic_features[0]].notna()).sum()
                print(f"基本特征覆盖: {basic_coverage}/{len(df)} 个基因组")
            
            if go_features:
                go_coverage = (df[go_features[0]].notna()).sum()
                print(f"GO特征覆盖: {go_coverage}/{len(df)} 个基因组")
            
            print("✅ 集成特征提取器测试完成")
        else:
            print("❌ 集成特征提取器测试失败")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_metadata_file):
            os.remove(test_metadata_file)

def test_checkpoint_recovery():
    """测试断点恢复功能"""
    print("\n=== 测试断点恢复功能 ===")
    
    # 创建测试处理器
    processor = IntegratedGenomeFeatureExtractor(
        base_dir="data_downloads",
        output_dir="test_checkpoint_output",
        max_workers=1,
        checkpoint_interval=1  # 每个基因组都保存进度
    )
    
    # 模拟已完成的基因组
    processor.completed_genomes.add('GCA_000219855.1')
    processor.save_progress()
    
    # 创建新的处理器实例（模拟重启）
    processor2 = IntegratedGenomeFeatureExtractor(
        base_dir="data_downloads",
        output_dir="test_checkpoint_output",
        max_workers=1,
        checkpoint_interval=1
    )
    
    # 检查是否正确加载了进度
    if 'GCA_000219855.1' in processor2.completed_genomes:
        print("✅ 断点恢复功能正常")
    else:
        print("❌ 断点恢复功能异常")

def main():
    """主测试函数"""
    print("GO注释功能特征提取测试")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_go_database()
        test_go_annotation_parser()
        test_go_feature_extractor()
        
        # 处理器测试
        test_go_genome_processor()
        test_integrated_feature_extractor()
        
        # 高级功能测试
        test_checkpoint_recovery()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成!")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
