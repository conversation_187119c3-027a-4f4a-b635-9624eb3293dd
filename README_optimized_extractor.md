# 优化的基因组特征提取器

## 📋 概述

这是一个优化的基因组特征提取系统，能够从data_downloads目录下的基因序列和NCBI注释文件中提取CDS、tRNA、rRNA等多种特征，支持多线程处理和大规模数据分析。

## 🚀 快速开始

### 1. 环境设置
```bash
# 创建conda环境
bash setup_conda_environment.sh

# 激活环境
conda activate genome_analysis
```

### 2. 测试运行
```bash
# 测试前3个基因组
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-genomes 3
```

### 3. 完整运行
```bash
# 处理所有基因组
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-workers 4
```

## 📁 文件结构

```
.
├── optimized_genome_feature_extractor.py  # 主要提取脚本
├── run_feature_extraction_example.py      # 使用示例脚本
├── environment.yml                         # Conda环境配置
├── setup_conda_environment.sh             # 环境设置脚本
├── README_optimized_extractor.md          # 本文件
└── data_downloads/                         # 基因组数据目录
    ├── metadata-v2.tsv                     # 元数据文件
    ├── Bacteria/                           # 细菌基因组
    ├── Archaea/                            # 古菌基因组
    └── ...
```

## 🧬 提取的特征类型

| 特征类别 | 数量 | 描述 |
|---------|------|------|
| 基因组基本特征 | 2个 | 基因组大小、contig数量 |
| 核苷酸组成特征 | 5个 | GC含量、AT偏斜等 |
| 二核苷酸特征 | 16个 | 所有二核苷酸频率 |
| CDS特征 | 5个 | CDS数量、长度、GC含量 |
| 密码子特征 | 61个 | 所有密码子频率 |
| 密码子使用特征 | 5个 | ENC、CUB、GC123等 |
| 氨基酸特征 | 20个 | 20种氨基酸频率 |
| 蛋白质理化特征 | 7个 | 疏水性、电荷、分子量等 |
| tRNA特征 | 4个 | tRNA数量、长度、GC含量 |
| rRNA特征 | 8个 | rRNA数量、类型、特征 |
| 基因组结构特征 | 3个 | 基因密度、链偏好等 |
| **总计** | **156个** | **全面的基因组特征** |

## 🔧 命令行参数

```bash
python optimized_genome_feature_extractor.py [metadata_file] [选项]

必需参数:
  metadata_file              metadata-v2.tsv文件路径

可选参数:
  --base-dir DIR             基因组数据目录 (默认: data_downloads)
  --output-file FILE         输出文件路径 (默认: 自动生成)
  --max-genomes N            最大处理基因组数量（用于测试）
  --max-workers N            最大工作线程数 (默认: 4)
  --no-threading             禁用多线程，使用顺序处理
  --help                     显示帮助信息
```

## 📊 使用示例

### 基本使用
```bash
# 处理所有基因组
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv

# 测试模式（前10个基因组）
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-genomes 10

# 多线程处理
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-workers 6

# 指定输出文件
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --output-file my_features.csv
```

### 高级配置
```bash
# 高性能配置
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv \
    --max-workers 8 \
    --output-file genome_features_full.csv

# 保守配置（单线程）
python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv \
    --no-threading \
    --max-genomes 100
```

### 交互式示例
```bash
# 运行示例脚本
python run_feature_extraction_example.py
```

## 📈 性能表现

### 测试结果
- **处理速度**: 3个基因组约2分钟
- **特征数量**: 156个不同类型特征
- **成功率**: 66.7% (2/3个基因组成功)
- **内存使用**: 适中，支持普通服务器

### 预估性能
- **小规模** (< 100个基因组): 2-4线程，约10-30分钟
- **中等规模** (100-1000个基因组): 4-6线程，约1-3小时
- **大规模** (> 1000个基因组): 6-8线程，约3-8小时

## 🛠️ 技术特点

### 1. 智能文件处理
- 自动在多个目录中查找基因组文件
- 支持压缩和非压缩格式
- 兼容GFF和GTF注释格式

### 2. 多线程架构
- 线程安全的并发处理
- 自动负载均衡
- 实时进度监控

### 3. 特征计算
- 基于BioPython的序列分析
- 实现ENC和CUB算法
- 氨基酸理化性质分析

### 4. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 单个失败不影响整体

## 🔍 输出文件

### 特征数据文件 (.csv)
```csv
genome_id,optimal_temperature,kingdom,genome_size,gc_content,cds_count,...
GCA_000219855.1,50.0,Bacteria,2847078,0.654,2667,...
GCA_000283575.1,30.0,Bacteria,4639221,0.540,4234,...
```

### 处理报告 (_report.txt)
```
基因组特征提取报告
==================================================

处理时间: 2025-07-14 20:04:38
总基因组数: 3
成功处理: 2
失败数量: 1
成功率: 66.7%
```

## 🔬 应用场景

### 1. 温度适应性研究
- 分析不同温度类型微生物的分子特征
- 构建温度预测模型
- 研究极端环境适应机制

### 2. 比较基因组学
- 物种间基因组特征比较
- 进化关系分析
- 功能基因组学研究

### 3. 机器学习应用
- 特征工程和数据预处理
- 分类和回归模型训练
- 生物信息学预测

## 📚 后续分析

### 数据加载和预处理
```python
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler

# 读取特征数据
df = pd.read_csv('genome_features.csv')

# 分离数值特征
numeric_features = df.select_dtypes(include=[np.number])

# 标准化
scaler = StandardScaler()
features_scaled = scaler.fit_transform(numeric_features)
```

### 探索性数据分析
```python
import matplotlib.pyplot as plt
import seaborn as sns

# 特征分布
plt.figure(figsize=(15, 10))
df[['gc_content', 'cds_gc_content', 'avg_trna_gc', 'avg_rrna_gc']].hist(bins=20)
plt.suptitle('GC含量分布')
plt.show()

# 温度与特征关系
plt.figure(figsize=(12, 8))
sns.scatterplot(data=df, x='optimal_temperature', y='gc_content', hue='kingdom')
plt.title('最适温度与GC含量关系')
plt.show()
```

### 机器学习建模
```python
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

# 准备数据
X = df.drop(['genome_id', 'optimal_temperature', 'kingdom'], axis=1)
y = df['optimal_temperature']

# 分割数据
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 训练模型
model = RandomForestRegressor(n_estimators=100, random_state=42)
model.fit(X_train, y_train)

# 预测和评估
y_pred = model.predict(X_test)
mse = mean_squared_error(y_test, y_pred)
r2 = r2_score(y_test, y_pred)

print(f'MSE: {mse:.2f}')
print(f'R²: {r2:.3f}')

# 特征重要性
feature_importance = pd.DataFrame({
    'feature': X.columns,
    'importance': model.feature_importances_
}).sort_values('importance', ascending=False)

print("Top 10 重要特征:")
print(feature_importance.head(10))
```

## 🛠️ 故障排除

### 常见问题

#### 1. 环境问题
```bash
# 检查conda环境
conda info --envs

# 重新创建环境
conda env remove -n genome_analysis
bash setup_conda_environment.sh
```

#### 2. 文件路径问题
```bash
# 检查数据目录结构
ls -la data_downloads/
ls -la data_downloads/Bacteria/

# 检查metadata文件
head data_downloads/metadata-v2.tsv
```

#### 3. 内存不足
```bash
# 减少线程数
--max-workers 2

# 限制处理数量
--max-genomes 50
```

#### 4. BioPython问题
```bash
# 重新安装BioPython
conda activate genome_analysis
conda install -c bioconda biopython
```

### 日志分析
脚本会自动生成详细的日志信息，包括：
- 处理进度
- 错误信息
- 性能统计
- 特征提取详情

## 📞 技术支持

如遇到问题，请：
1. 检查conda环境是否正确激活
2. 确认数据目录结构是否正确
3. 查看生成的报告文件了解详细信息
4. 使用`--max-genomes 1`进行单个基因组测试

## 🎯 总结

这个优化的基因组特征提取器提供了：

1. **完整的特征集** - 156个不同类型的基因组特征
2. **高性能处理** - 多线程并发和内存优化
3. **易于使用** - 自动化环境设置和详细文档
4. **可扩展性** - 支持大规模数据处理
5. **可靠性** - 完善的错误处理和日志记录

适用于基因组学研究、温度适应性分析、比较基因组学和机器学习等多个领域。
