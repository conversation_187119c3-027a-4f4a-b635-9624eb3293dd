#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GO注释功能特征提取器
支持多线程处理和断点恢复功能

主要功能：
1. 从GFF/GTF注释文件中提取GO注释信息
2. 分析GO功能分类（分子功能、生物过程、细胞组分）
3. 计算GO功能富集和多样性指标
4. 支持多线程并发处理
5. 支持断点恢复机制
6. 生成详细的GO功能分析报告

作者: AI Assistant
日期: 2025-07-14
"""

import os
import sys
import gzip
import json
import argparse
import pandas as pd
import numpy as np
import logging
import threading
import time
import requests
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple, Union
from collections import Counter, defaultdict
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import pickle
import re

# 尝试导入额外的包
try:
    from Bio import SeqIO
    from Bio.Seq import Seq
    BIOPYTHON_AVAILABLE = True
except ImportError:
    BIOPYTHON_AVAILABLE = False
    print("警告: BioPython未安装，某些功能将不可用")

try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("警告: requests未安装，在线GO数据库查询将不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class GODatabase:
    """GO数据库管理器"""
    
    def __init__(self, cache_dir: str = "go_cache"):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        
        # GO术语缓存
        self.go_terms = {}
        self.go_hierarchy = defaultdict(set)  # parent -> children
        self.go_ancestors = defaultdict(set)  # term -> all ancestors
        
        # GO分类
        self.go_categories = {
            'molecular_function': 'GO:0003674',
            'biological_process': 'GO:0008150', 
            'cellular_component': 'GO:0005575'
        }
        
        # 加载本地GO数据
        self.load_cached_data()
    
    def load_cached_data(self):
        """加载缓存的GO数据"""
        cache_file = self.cache_dir / "go_terms_cache.pkl"
        
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    self.go_terms = cached_data.get('go_terms', {})
                    self.go_hierarchy = cached_data.get('go_hierarchy', defaultdict(set))
                    self.go_ancestors = cached_data.get('go_ancestors', defaultdict(set))
                
                logger.info(f"加载缓存GO数据: {len(self.go_terms)} 个术语")
                return True
            except Exception as e:
                logger.warning(f"加载GO缓存失败: {e}")
        
        # 如果没有缓存，加载基本GO分类
        self._load_basic_go_terms()
        return False
    
    def save_cached_data(self):
        """保存GO数据到缓存"""
        cache_file = self.cache_dir / "go_terms_cache.pkl"
        
        try:
            cached_data = {
                'go_terms': dict(self.go_terms),
                'go_hierarchy': dict(self.go_hierarchy),
                'go_ancestors': dict(self.go_ancestors)
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(cached_data, f)
            
            logger.info(f"保存GO缓存: {len(self.go_terms)} 个术语")
        except Exception as e:
            logger.error(f"保存GO缓存失败: {e}")
    
    def _load_basic_go_terms(self):
        """加载基本GO术语"""
        basic_terms = {
            'GO:0003674': {'name': 'molecular_function', 'namespace': 'molecular_function'},
            'GO:0008150': {'name': 'biological_process', 'namespace': 'biological_process'},
            'GO:0005575': {'name': 'cellular_component', 'namespace': 'cellular_component'},
            
            # 一些常见的GO术语
            'GO:0003824': {'name': 'catalytic activity', 'namespace': 'molecular_function'},
            'GO:0005488': {'name': 'binding', 'namespace': 'molecular_function'},
            'GO:0005215': {'name': 'transporter activity', 'namespace': 'molecular_function'},
            'GO:0030234': {'name': 'enzyme regulator activity', 'namespace': 'molecular_function'},
            
            'GO:0008152': {'name': 'metabolic process', 'namespace': 'biological_process'},
            'GO:0009987': {'name': 'cellular process', 'namespace': 'biological_process'},
            'GO:0065007': {'name': 'biological regulation', 'namespace': 'biological_process'},
            'GO:0050896': {'name': 'response to stimulus', 'namespace': 'biological_process'},
            
            'GO:0005623': {'name': 'cell', 'namespace': 'cellular_component'},
            'GO:0005622': {'name': 'intracellular', 'namespace': 'cellular_component'},
            'GO:0005576': {'name': 'extracellular region', 'namespace': 'cellular_component'},
            'GO:0032991': {'name': 'protein-containing complex', 'namespace': 'cellular_component'}
        }
        
        self.go_terms.update(basic_terms)
        logger.info(f"加载基本GO术语: {len(basic_terms)} 个")
    
    def get_go_category(self, go_id: str) -> str:
        """获取GO术语的分类"""
        if go_id in self.go_terms:
            return self.go_terms[go_id].get('namespace', 'unknown')
        
        # 基于GO ID前缀推断（不完全准确，但作为备选）
        if go_id.startswith('GO:'):
            # 这里可以添加更复杂的分类逻辑
            pass
        
        return 'unknown'
    
    def get_go_name(self, go_id: str) -> str:
        """获取GO术语名称"""
        if go_id in self.go_terms:
            return self.go_terms[go_id].get('name', go_id)
        return go_id
    
    def add_go_term(self, go_id: str, name: str, namespace: str):
        """添加GO术语"""
        self.go_terms[go_id] = {
            'name': name,
            'namespace': namespace
        }

class GOAnnotationParser:
    """GO注释解析器"""
    
    def __init__(self, go_database: GODatabase):
        self.go_db = go_database
        
        # GO注释的常见模式
        self.go_patterns = [
            r'GO:(\d{7})',  # 标准GO ID格式
            r'GO_(\d{7})',  # 下划线格式
            r'GO\.(\d{7})'  # 点号格式
        ]
    
    def parse_gff_go_annotations(self, gff_file: str) -> Dict[str, List[str]]:
        """从GFF文件解析GO注释"""
        logger.info(f"解析GO注释: {gff_file}")
        
        go_annotations = defaultdict(list)
        gene_go_map = defaultdict(set)
        
        try:
            # 处理压缩文件
            if gff_file.endswith('.gz'):
                file_handle = gzip.open(gff_file, 'rt', encoding='utf-8')
            else:
                file_handle = open(gff_file, 'r', encoding='utf-8')
            
            with file_handle as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 跳过注释行和空行
                    if line.startswith('#') or not line:
                        continue
                    
                    try:
                        parts = line.split('\t')
                        if len(parts) < 9:
                            continue
                        
                        seqname, source, feature_type, start, end, score, strand, frame, attributes = parts
                        
                        # 解析属性
                        attr_dict = self._parse_attributes(attributes)
                        
                        # 提取基因ID
                        gene_id = self._extract_gene_id(attr_dict)
                        
                        # 查找GO注释
                        go_terms = self._extract_go_terms(attributes, attr_dict)
                        
                        if gene_id and go_terms:
                            for go_term in go_terms:
                                gene_go_map[gene_id].add(go_term)
                                go_annotations[go_term].append(gene_id)
                    
                    except Exception as e:
                        logger.warning(f"解析第{line_num}行失败: {e}")
                        continue
            
            # 转换为普通字典
            result = {
                'gene_go_map': {gene: list(go_set) for gene, go_set in gene_go_map.items()},
                'go_gene_map': dict(go_annotations)
            }
            
            logger.info(f"解析完成: {len(gene_go_map)} 个基因, {len(go_annotations)} 个GO术语")
            
        except Exception as e:
            logger.error(f"解析GO注释失败: {e}")
            result = {'gene_go_map': {}, 'go_gene_map': {}}
        
        return result
    
    def _parse_attributes(self, attributes: str) -> Dict[str, str]:
        """解析GFF属性字段"""
        attr_dict = {}
        
        try:
            # 处理不同的属性格式
            if '=' in attributes:
                # GFF3格式: key=value;key2=value2
                for attr in attributes.split(';'):
                    if '=' in attr:
                        key, value = attr.split('=', 1)
                        attr_dict[key.strip()] = value.strip().strip('"')
            else:
                # GTF格式: key "value"; key2 "value2";
                import re
                pattern = r'(\w+)\s+"([^"]*)"'
                matches = re.findall(pattern, attributes)
                for key, value in matches:
                    attr_dict[key] = value
        
        except Exception as e:
            logger.warning(f"解析属性失败: {e}")
        
        return attr_dict
    
    def _extract_gene_id(self, attr_dict: Dict[str, str]) -> Optional[str]:
        """提取基因ID"""
        # 尝试不同的基因ID字段
        id_fields = ['gene_id', 'ID', 'Name', 'locus_tag', 'gene', 'protein_id']
        
        for field in id_fields:
            if field in attr_dict:
                return attr_dict[field]
        
        return None
    
    def _extract_go_terms(self, raw_attributes: str, attr_dict: Dict[str, str]) -> Set[str]:
        """提取GO术语"""
        go_terms = set()
        
        # 在原始属性字符串中搜索GO术语
        for pattern in self.go_patterns:
            matches = re.findall(pattern, raw_attributes)
            for match in matches:
                go_id = f"GO:{match}"
                go_terms.add(go_id)
        
        # 在特定属性字段中查找
        go_fields = ['Ontology_term', 'go_id', 'GO', 'gene_ontology', 'Dbxref']
        
        for field in go_fields:
            if field in attr_dict:
                value = attr_dict[field]
                # 解析多个GO术语
                for pattern in self.go_patterns:
                    matches = re.findall(pattern, value)
                    for match in matches:
                        go_id = f"GO:{match}"
                        go_terms.add(go_id)
        
        return go_terms

class GOFeatureExtractor:
    """GO功能特征提取器"""
    
    def __init__(self, go_database: GODatabase):
        self.go_db = go_database
    
    def extract_go_features(self, go_annotations: Dict[str, List[str]]) -> Dict[str, float]:
        """提取GO功能特征"""
        features = {}
        
        try:
            gene_go_map = go_annotations.get('gene_go_map', {})
            go_gene_map = go_annotations.get('go_gene_map', {})
            
            if not gene_go_map:
                logger.warning("没有找到GO注释数据")
                return self._get_empty_features()
            
            # 基本统计
            features.update(self._extract_basic_go_stats(gene_go_map, go_gene_map))
            
            # 分类统计
            features.update(self._extract_category_stats(gene_go_map))
            
            # 功能多样性
            features.update(self._extract_diversity_features(gene_go_map, go_gene_map))
            
            # 功能富集
            features.update(self._extract_enrichment_features(go_gene_map))
            
            # 层次结构特征
            features.update(self._extract_hierarchy_features(gene_go_map))
            
            logger.info(f"提取GO特征完成: {len(features)} 个特征")
            
        except Exception as e:
            logger.error(f"GO特征提取失败: {e}")
            features = self._get_empty_features()
        
        return features

    def _extract_basic_go_stats(self, gene_go_map: Dict[str, List[str]],
                               go_gene_map: Dict[str, List[str]]) -> Dict[str, float]:
        """提取基本GO统计特征"""
        features = {}

        # 基因和GO术语数量
        total_genes = len(gene_go_map)
        total_go_terms = len(go_gene_map)

        features['total_annotated_genes'] = total_genes
        features['total_go_terms'] = total_go_terms

        if total_genes > 0:
            # 每个基因的平均GO术语数
            go_counts_per_gene = [len(go_list) for go_list in gene_go_map.values()]
            features['avg_go_per_gene'] = np.mean(go_counts_per_gene)
            features['median_go_per_gene'] = np.median(go_counts_per_gene)
            features['std_go_per_gene'] = np.std(go_counts_per_gene)
            features['max_go_per_gene'] = max(go_counts_per_gene)
            features['min_go_per_gene'] = min(go_counts_per_gene)

        if total_go_terms > 0:
            # 每个GO术语的平均基因数
            gene_counts_per_go = [len(gene_list) for gene_list in go_gene_map.values()]
            features['avg_genes_per_go'] = np.mean(gene_counts_per_go)
            features['median_genes_per_go'] = np.median(gene_counts_per_go)
            features['std_genes_per_go'] = np.std(gene_counts_per_go)
            features['max_genes_per_go'] = max(gene_counts_per_go)
            features['min_genes_per_go'] = min(gene_counts_per_go)

        return features

    def _extract_category_stats(self, gene_go_map: Dict[str, List[str]]) -> Dict[str, float]:
        """提取GO分类统计特征"""
        features = {}

        # 按分类统计GO术语
        category_counts = defaultdict(int)
        category_genes = defaultdict(set)

        for gene, go_terms in gene_go_map.items():
            for go_term in go_terms:
                category = self.go_db.get_go_category(go_term)
                category_counts[category] += 1
                category_genes[category].add(gene)

        # 各分类的统计
        total_annotations = sum(category_counts.values())
        total_genes = len(gene_go_map)

        for category in ['molecular_function', 'biological_process', 'cellular_component', 'unknown']:
            # GO术语数量和比例
            count = category_counts[category]
            features[f'{category}_go_count'] = count
            features[f'{category}_go_ratio'] = count / total_annotations if total_annotations > 0 else 0

            # 注释基因数量和比例
            gene_count = len(category_genes[category])
            features[f'{category}_gene_count'] = gene_count
            features[f'{category}_gene_ratio'] = gene_count / total_genes if total_genes > 0 else 0

        # 分类多样性
        if total_annotations > 0:
            category_probs = [count / total_annotations for count in category_counts.values() if count > 0]
            if category_probs:
                # Shannon多样性指数
                shannon_diversity = -sum(p * np.log2(p) for p in category_probs)
                features['go_category_diversity'] = shannon_diversity

                # Simpson多样性指数
                simpson_diversity = 1 - sum(p**2 for p in category_probs)
                features['go_category_simpson'] = simpson_diversity

        return features

    def _extract_diversity_features(self, gene_go_map: Dict[str, List[str]],
                                  go_gene_map: Dict[str, List[str]]) -> Dict[str, float]:
        """提取功能多样性特征"""
        features = {}

        # GO术语频率分布
        go_frequencies = [len(gene_list) for gene_list in go_gene_map.values()]

        if go_frequencies:
            total_annotations = sum(go_frequencies)
            go_probs = [freq / total_annotations for freq in go_frequencies]

            # Shannon多样性指数
            shannon_diversity = -sum(p * np.log2(p) for p in go_probs if p > 0)
            features['go_shannon_diversity'] = shannon_diversity

            # Simpson多样性指数
            simpson_diversity = 1 - sum(p**2 for p in go_probs)
            features['go_simpson_diversity'] = simpson_diversity

            # 均匀度指数
            max_diversity = np.log2(len(go_probs))
            features['go_evenness'] = shannon_diversity / max_diversity if max_diversity > 0 else 0

        # 基因功能多样性
        gene_diversities = []
        for gene, go_terms in gene_go_map.items():
            if len(go_terms) > 1:
                # 计算该基因的功能多样性（基于GO分类）
                categories = [self.go_db.get_go_category(go_term) for go_term in go_terms]
                category_counts = Counter(categories)

                if len(category_counts) > 1:
                    total = sum(category_counts.values())
                    probs = [count / total for count in category_counts.values()]
                    gene_diversity = -sum(p * np.log2(p) for p in probs if p > 0)
                    gene_diversities.append(gene_diversity)

        if gene_diversities:
            features['avg_gene_functional_diversity'] = np.mean(gene_diversities)
            features['std_gene_functional_diversity'] = np.std(gene_diversities)

        return features

    def _extract_enrichment_features(self, go_gene_map: Dict[str, List[str]]) -> Dict[str, float]:
        """提取功能富集特征"""
        features = {}

        # GO术语频率分析
        go_frequencies = {go_term: len(gene_list) for go_term, gene_list in go_gene_map.items()}

        if go_frequencies:
            frequencies = list(go_frequencies.values())

            # 频率统计
            features['go_freq_mean'] = np.mean(frequencies)
            features['go_freq_std'] = np.std(frequencies)
            features['go_freq_skewness'] = self._calculate_skewness(frequencies)
            features['go_freq_kurtosis'] = self._calculate_kurtosis(frequencies)

            # 高频GO术语比例
            total_terms = len(frequencies)
            high_freq_threshold = np.percentile(frequencies, 75)
            high_freq_count = sum(1 for freq in frequencies if freq >= high_freq_threshold)
            features['high_freq_go_ratio'] = high_freq_count / total_terms if total_terms > 0 else 0

            # 稀有GO术语比例
            low_freq_threshold = np.percentile(frequencies, 25)
            low_freq_count = sum(1 for freq in frequencies if freq <= low_freq_threshold)
            features['low_freq_go_ratio'] = low_freq_count / total_terms if total_terms > 0 else 0

            # 单基因GO术语比例
            singleton_count = sum(1 for freq in frequencies if freq == 1)
            features['singleton_go_ratio'] = singleton_count / total_terms if total_terms > 0 else 0

        return features

    def _extract_hierarchy_features(self, gene_go_map: Dict[str, List[str]]) -> Dict[str, float]:
        """提取GO层次结构特征"""
        features = {}

        # 这里可以添加GO层次结构分析
        # 由于需要完整的GO层次结构数据，这里提供基础实现

        # GO术语特异性分析（基于GO ID数字部分，简化方法）
        go_specificities = []

        for gene, go_terms in gene_go_map.items():
            for go_term in go_terms:
                if go_term.startswith('GO:'):
                    try:
                        go_number = int(go_term.split(':')[1])
                        # 简化的特异性评估：数字越大通常越特异
                        specificity = go_number / 9999999  # 标准化到0-1
                        go_specificities.append(specificity)
                    except:
                        continue

        if go_specificities:
            features['avg_go_specificity'] = np.mean(go_specificities)
            features['std_go_specificity'] = np.std(go_specificities)
            features['median_go_specificity'] = np.median(go_specificities)

        return features

    def _calculate_skewness(self, data: List[float]) -> float:
        """计算偏度"""
        if len(data) < 3:
            return 0.0

        mean = np.mean(data)
        std = np.std(data)

        if std == 0:
            return 0.0

        skewness = np.mean([((x - mean) / std) ** 3 for x in data])
        return skewness

    def _calculate_kurtosis(self, data: List[float]) -> float:
        """计算峰度"""
        if len(data) < 4:
            return 0.0

        mean = np.mean(data)
        std = np.std(data)

        if std == 0:
            return 0.0

        kurtosis = np.mean([((x - mean) / std) ** 4 for x in data]) - 3
        return kurtosis

    def _get_empty_features(self) -> Dict[str, float]:
        """返回空特征字典"""
        empty_features = {}

        # 基本统计特征
        basic_features = [
            'total_annotated_genes', 'total_go_terms', 'avg_go_per_gene',
            'median_go_per_gene', 'std_go_per_gene', 'max_go_per_gene', 'min_go_per_gene',
            'avg_genes_per_go', 'median_genes_per_go', 'std_genes_per_go',
            'max_genes_per_go', 'min_genes_per_go'
        ]

        # 分类特征
        categories = ['molecular_function', 'biological_process', 'cellular_component', 'unknown']
        category_features = []
        for cat in categories:
            category_features.extend([
                f'{cat}_go_count', f'{cat}_go_ratio',
                f'{cat}_gene_count', f'{cat}_gene_ratio'
            ])

        # 多样性特征
        diversity_features = [
            'go_category_diversity', 'go_category_simpson', 'go_shannon_diversity',
            'go_simpson_diversity', 'go_evenness', 'avg_gene_functional_diversity',
            'std_gene_functional_diversity'
        ]

        # 富集特征
        enrichment_features = [
            'go_freq_mean', 'go_freq_std', 'go_freq_skewness', 'go_freq_kurtosis',
            'high_freq_go_ratio', 'low_freq_go_ratio', 'singleton_go_ratio'
        ]

        # 层次特征
        hierarchy_features = [
            'avg_go_specificity', 'std_go_specificity', 'median_go_specificity'
        ]

        all_features = basic_features + category_features + diversity_features + enrichment_features + hierarchy_features

        for feature in all_features:
            empty_features[feature] = 0.0

        return empty_features

class GOGenomeProcessor:
    """GO基因组处理器 - 支持多线程和断点恢复"""

    def __init__(self, base_dir: str = "data_downloads", output_dir: str = "go_features_output",
                 max_workers: int = 4, checkpoint_interval: int = 50):
        self.base_dir = Path(base_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # 多线程配置
        self.max_workers = max_workers
        self.thread_lock = threading.Lock()
        self.checkpoint_interval = checkpoint_interval

        # GO数据库和解析器
        self.go_db = GODatabase(cache_dir=self.output_dir / "go_cache")
        self.go_parser = GOAnnotationParser(self.go_db)
        self.go_extractor = GOFeatureExtractor(self.go_db)

        # 进度跟踪
        self.progress_file = self.output_dir / "go_extraction_progress.json"
        self.completed_genomes = set()
        self.load_progress()

        # 统计信息
        self.stats = {
            'total_genomes': 0,
            'processed_genomes': 0,
            'failed_genomes': 0,
            'genomes_with_go': 0,
            'genomes_without_go': 0,
            'errors': []
        }

        # 设置日志
        self.setup_logging()

    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / f"go_extraction_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))

        # 添加到logger
        logger.addHandler(file_handler)

        self.log_file = log_file

    def load_progress(self):
        """加载进度文件"""
        if self.progress_file.exists():
            try:
                with open(self.progress_file, 'r') as f:
                    progress_data = json.load(f)
                    self.completed_genomes = set(progress_data.get('completed_genomes', []))
                logger.info(f"加载进度: 已完成 {len(self.completed_genomes)} 个基因组")
            except Exception as e:
                logger.warning(f"加载进度文件失败: {e}")

    def save_progress(self):
        """保存进度"""
        try:
            progress_data = {
                'completed_genomes': list(self.completed_genomes),
                'last_update': datetime.now().isoformat(),
                'stats': dict(self.stats)
            }
            with open(self.progress_file, 'w') as f:
                json.dump(progress_data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"保存进度失败: {e}")

    def find_genome_files(self, genome_id: str) -> Tuple[Optional[str], Optional[str]]:
        """查找基因组文件和注释文件"""
        # 在各个子目录中搜索
        for subdir in ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']:
            genome_dir = self.base_dir / subdir / genome_id

            if genome_dir.exists():
                # 查找基因组序列文件
                fna_file = genome_dir / f"{genome_id}_genomic.fna.gz"
                if not fna_file.exists():
                    fna_file = genome_dir / f"{genome_id}_genomic.fna"

                # 查找注释文件
                gff_file = genome_dir / f"{genome_id}_genomic.gff.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gff"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf.gz"
                if not gff_file.exists():
                    gff_file = genome_dir / f"{genome_id}_genomic.gtf"

                return (str(fna_file) if fna_file.exists() else None,
                       str(gff_file) if gff_file.exists() else None)

        return None, None

    def process_single_genome(self, genome_data: Tuple[int, pd.Series]) -> Dict:
        """处理单个基因组的GO注释"""
        idx, row = genome_data
        genome_id = row['genome_id']

        # 检查是否已完成
        if genome_id in self.completed_genomes:
            return {'genome_id': genome_id, 'status': 'skipped', 'reason': 'already_completed'}

        try:
            thread_id = threading.current_thread().ident
            logger.info(f"[线程{thread_id}] 处理基因组GO注释: {genome_id}")

            # 查找文件
            genome_file, annotation_file = self.find_genome_files(genome_id)

            if not annotation_file:
                return {
                    'genome_id': genome_id,
                    'status': 'failed',
                    'error': 'Annotation file not found',
                    'features': {}
                }

            # 解析GO注释
            go_annotations = self.go_parser.parse_gff_go_annotations(annotation_file)

            # 提取GO特征
            go_features = self.go_extractor.extract_go_features(go_annotations)

            # 添加元数据
            go_features['genome_id'] = genome_id
            if 'optimal_temperature' in row:
                go_features['optimal_temperature'] = row['optimal_temperature']
            if 'kingdom' in row:
                go_features['kingdom'] = row['kingdom']

            # 更新统计信息
            with self.thread_lock:
                self.stats['processed_genomes'] += 1

                if go_features.get('total_annotated_genes', 0) > 0:
                    self.stats['genomes_with_go'] += 1
                else:
                    self.stats['genomes_without_go'] += 1

                # 标记为已完成
                self.completed_genomes.add(genome_id)

                # 定期保存进度
                if len(self.completed_genomes) % self.checkpoint_interval == 0:
                    self.save_progress()
                    self.go_db.save_cached_data()  # 保存GO缓存

            return {
                'genome_id': genome_id,
                'status': 'success',
                'error': None,
                'features': go_features
            }

        except Exception as e:
            error_msg = f"处理基因组 {genome_id} 时出错: {e}"
            logger.error(f"[线程{thread_id}] {error_msg}")

            with self.thread_lock:
                self.stats['failed_genomes'] += 1
                self.stats['errors'].append(error_msg)

            return {
                'genome_id': genome_id,
                'status': 'failed',
                'error': str(e),
                'features': {}
            }

    def process_metadata_file(self, metadata_file: str, output_file: str = None,
                            max_genomes: int = None, use_threading: bool = True) -> str:
        """处理metadata文件，提取所有基因组的GO特征"""
        logger.info(f"开始处理GO注释特征: {metadata_file}")
        logger.info(f"多线程模式: {'启用' if use_threading else '禁用'} (工作线程: {self.max_workers})")

        # 读取metadata文件
        try:
            df = pd.read_csv(metadata_file, sep='\t')
            logger.info(f"读取到 {len(df)} 个基因组")
        except Exception as e:
            logger.error(f"读取metadata文件失败: {e}")
            return None

        # 限制处理数量
        if max_genomes:
            df = df.head(max_genomes)
            logger.info(f"限制处理前 {max_genomes} 个基因组")

        self.stats['total_genomes'] = len(df)

        # 过滤已完成的基因组
        remaining_df = df[~df['genome_id'].isin(self.completed_genomes)]
        logger.info(f"剩余待处理: {len(remaining_df)} 个基因组")

        if remaining_df.empty:
            logger.info("所有基因组已处理完成!")
            return self._load_existing_results(output_file)

        # 处理数据
        all_features = []

        if use_threading and self.max_workers > 1:
            all_features = self._process_with_threading(remaining_df)
        else:
            all_features = self._process_sequentially(remaining_df)

        # 合并已有结果
        existing_features = self._load_existing_results()
        if existing_features:
            all_features.extend(existing_features)

        # 保存结果
        if all_features:
            if output_file is None:
                output_file = self.output_dir / f"go_features_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            features_df = pd.DataFrame(all_features)
            features_df.to_csv(output_file, index=False)
            logger.info(f"GO特征数据已保存到: {output_file}")

            # 生成统计报告
            self._generate_report(str(output_file).replace('.csv', '_report.txt'))

            # 最终保存进度和缓存
            self.save_progress()
            self.go_db.save_cached_data()

            return str(output_file)
        else:
            logger.error("没有成功提取任何GO特征")
            return None

    def _process_with_threading(self, df: pd.DataFrame) -> List[Dict]:
        """多线程处理"""
        logger.info(f"使用多线程处理，工作线程数: {self.max_workers}")

        all_features = []

        # 准备任务
        tasks = [(idx, row) for idx, row in df.iterrows()]

        # 使用线程池
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_task = {executor.submit(self.process_single_genome, task): task for task in tasks}

            completed_count = 0
            for future in as_completed(future_to_task):
                try:
                    result = future.result()
                    completed_count += 1

                    if result['status'] == 'success':
                        all_features.append(result['features'])

                    # 进度报告
                    if completed_count % 10 == 0:
                        progress = (completed_count / len(tasks)) * 100
                        logger.info(f"进度: {completed_count}/{len(tasks)} ({progress:.1f}%)")

                except Exception as e:
                    logger.error(f"线程任务执行失败: {e}")

        return all_features

    def _process_sequentially(self, df: pd.DataFrame) -> List[Dict]:
        """顺序处理"""
        logger.info("使用顺序处理模式")

        all_features = []

        for idx, (_, row) in enumerate(df.iterrows()):
            result = self.process_single_genome((idx, row))

            if result['status'] == 'success':
                all_features.append(result['features'])

            # 进度报告
            if (idx + 1) % 10 == 0:
                progress = ((idx + 1) / len(df)) * 100
                logger.info(f"进度: {idx + 1}/{len(df)} ({progress:.1f}%)")

        return all_features

    def _load_existing_results(self, output_file: str = None) -> List[Dict]:
        """加载已有的结果"""
        existing_features = []

        # 查找已有的结果文件
        if output_file and os.path.exists(output_file):
            result_files = [output_file]
        else:
            result_files = list(self.output_dir.glob("go_features_*.csv"))

        for result_file in result_files:
            try:
                df = pd.read_csv(result_file)
                existing_features.extend(df.to_dict('records'))
                logger.info(f"加载已有结果: {len(df)} 个基因组从 {result_file}")
            except Exception as e:
                logger.warning(f"加载结果文件失败 {result_file}: {e}")

        return existing_features

    def _generate_report(self, report_file: str):
        """生成处理报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("GO注释特征提取报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总基因组数: {self.stats['total_genomes']}\n")
            f.write(f"成功处理: {self.stats['processed_genomes']}\n")
            f.write(f"失败数量: {self.stats['failed_genomes']}\n")
            f.write(f"成功率: {(self.stats['processed_genomes']/max(self.stats['total_genomes'], 1)*100):.1f}%\n\n")

            f.write("GO注释统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"有GO注释的基因组: {self.stats['genomes_with_go']}\n")
            f.write(f"无GO注释的基因组: {self.stats['genomes_without_go']}\n")

            if self.stats['processed_genomes'] > 0:
                go_coverage = (self.stats['genomes_with_go'] / self.stats['processed_genomes']) * 100
                f.write(f"GO注释覆盖率: {go_coverage:.1f}%\n")

            f.write(f"\nGO数据库统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"缓存的GO术语数: {len(self.go_db.go_terms)}\n")

            if self.stats['errors']:
                f.write(f"\n错误信息:\n")
                f.write("-" * 30 + "\n")
                for error in self.stats['errors'][:10]:  # 只显示前10个错误
                    f.write(f"{error}\n")
                if len(self.stats['errors']) > 10:
                    f.write(f"... 还有 {len(self.stats['errors']) - 10} 个错误\n")

        logger.info(f"生成处理报告: {report_file}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GO注释功能特征提取器')
    parser.add_argument('metadata_file', help='metadata-v2.tsv文件路径')
    parser.add_argument('--base-dir', default='data_downloads', help='基因组数据目录')
    parser.add_argument('--output-dir', default='go_features_output', help='输出目录')
    parser.add_argument('--output-file', help='输出文件路径')
    parser.add_argument('--max-genomes', type=int, help='最大处理基因组数量（用于测试）')
    parser.add_argument('--max-workers', type=int, default=4, help='最大工作线程数')
    parser.add_argument('--no-threading', action='store_true', help='禁用多线程')
    parser.add_argument('--checkpoint-interval', type=int, default=50, help='检查点保存间隔')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.metadata_file):
        logger.error(f"输入文件不存在: {args.metadata_file}")
        sys.exit(1)

    # 创建处理器
    processor = GOGenomeProcessor(
        base_dir=args.base_dir,
        output_dir=args.output_dir,
        max_workers=args.max_workers,
        checkpoint_interval=args.checkpoint_interval
    )

    # 开始处理
    try:
        output_file = processor.process_metadata_file(
            args.metadata_file,
            output_file=args.output_file,
            max_genomes=args.max_genomes,
            use_threading=not args.no_threading
        )

        if output_file:
            logger.info(f"GO特征提取完成! 结果保存在: {output_file}")
        else:
            logger.error("GO特征提取失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断处理")
        processor.save_progress()
        processor.go_db.save_cached_data()
        print("进度已保存，可以稍后继续")
        sys.exit(1)
    except Exception as e:
        logger.error(f"处理过程中出错: {e}")
        processor.save_progress()
        processor.go_db.save_cached_data()
        sys.exit(1)

if __name__ == "__main__":
    main()
