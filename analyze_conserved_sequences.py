#!/usr/bin/env python3
"""
分析提取的保守序列
包括序列统计、长度分布、GC含量分析等
"""

import pandas as pd
import numpy as np
from Bio import SeqIO
try:
    from Bio.SeqUtils import gc_fraction as GC
except ImportError:
    from Bio.SeqUtils import GC
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
from pathlib import Path
import argparse
import logging
from datetime import datetime
from collections import defaultdict, Counter

class ConservedSequenceAnalyzer:
    def __init__(self, sequences_dir="conserved_sequences_test", output_dir="sequence_analysis"):
        self.sequences_dir = Path(sequences_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 分析结果
        self.analysis_results = {}
    
    def setup_logging(self):
        """设置日志系统"""
        log_file = self.output_dir / f"analysis_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def analyze_sequence_file(self, fasta_file):
        """分析单个FASTA文件"""
        self.logger.info(f"分析文件: {fasta_file}")
        
        sequences = []
        genome_counts = defaultdict(int)
        
        try:
            with open(fasta_file, 'r') as f:
                for record in SeqIO.parse(f, 'fasta'):
                    seq_info = {
                        'id': record.id,
                        'description': record.description,
                        'length': len(record.seq),
                        'gc_content': GC(record.seq) * 100,  # 转换为百分比
                        'sequence': str(record.seq)
                    }
                    
                    # 提取基因组ID
                    if '|' in record.description:
                        genome_id = record.description.split('|')[0]
                        seq_info['genome_id'] = genome_id
                        genome_counts[genome_id] += 1
                    
                    sequences.append(seq_info)
        
        except Exception as e:
            self.logger.error(f"分析文件失败 {fasta_file}: {e}")
            return None
        
        if not sequences:
            self.logger.warning(f"文件中没有序列: {fasta_file}")
            return None
        
        # 计算统计信息
        lengths = [s['length'] for s in sequences]
        gc_contents = [s['gc_content'] for s in sequences]
        
        stats = {
            'file_name': fasta_file.name,
            'total_sequences': len(sequences),
            'unique_genomes': len(genome_counts),
            'length_stats': {
                'min': min(lengths),
                'max': max(lengths),
                'mean': np.mean(lengths),
                'median': np.median(lengths),
                'std': np.std(lengths)
            },
            'gc_stats': {
                'min': min(gc_contents),
                'max': max(gc_contents),
                'mean': np.mean(gc_contents),
                'median': np.median(gc_contents),
                'std': np.std(gc_contents)
            },
            'sequences': sequences,
            'genome_counts': dict(genome_counts)
        }
        
        return stats
    
    def create_visualizations(self, gene_type, stats):
        """创建可视化图表"""
        if not PLOTTING_AVAILABLE:
            self.logger.warning("matplotlib/seaborn未安装，跳过可视化")
            return

        try:
            
            # 设置图表样式
            plt.style.use('default')
            sns.set_palette("husl")
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'{gene_type} 序列分析', fontsize=16, fontweight='bold')
            
            # 1. 序列长度分布
            lengths = [s['length'] for s in stats['sequences']]
            axes[0, 0].hist(lengths, bins=20, alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('序列长度分布')
            axes[0, 0].set_xlabel('序列长度 (bp)')
            axes[0, 0].set_ylabel('频次')
            axes[0, 0].axvline(stats['length_stats']['mean'], color='red', linestyle='--', 
                              label=f"平均值: {stats['length_stats']['mean']:.1f}")
            axes[0, 0].legend()
            
            # 2. GC含量分布
            gc_contents = [s['gc_content'] for s in stats['sequences']]
            axes[0, 1].hist(gc_contents, bins=20, alpha=0.7, edgecolor='black', color='green')
            axes[0, 1].set_title('GC含量分布')
            axes[0, 1].set_xlabel('GC含量 (%)')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].axvline(stats['gc_stats']['mean'], color='red', linestyle='--',
                              label=f"平均值: {stats['gc_stats']['mean']:.1f}%")
            axes[0, 1].legend()
            
            # 3. 每个基因组的序列数量
            genome_counts = stats['genome_counts']
            if len(genome_counts) <= 20:  # 只显示前20个
                genomes = list(genome_counts.keys())
                counts = list(genome_counts.values())
                axes[1, 0].bar(range(len(genomes)), counts, alpha=0.7)
                axes[1, 0].set_title('每个基因组的序列数量')
                axes[1, 0].set_xlabel('基因组')
                axes[1, 0].set_ylabel('序列数量')
                axes[1, 0].set_xticks(range(len(genomes)))
                axes[1, 0].set_xticklabels([g[:10] + '...' if len(g) > 10 else g for g in genomes], 
                                          rotation=45, ha='right')
            else:
                # 如果基因组太多，显示序列数量分布
                count_values = list(genome_counts.values())
                axes[1, 0].hist(count_values, bins=10, alpha=0.7, edgecolor='black')
                axes[1, 0].set_title('基因组序列数量分布')
                axes[1, 0].set_xlabel('每个基因组的序列数量')
                axes[1, 0].set_ylabel('基因组数量')
            
            # 4. 长度vs GC含量散点图
            axes[1, 1].scatter(lengths, gc_contents, alpha=0.6)
            axes[1, 1].set_title('序列长度 vs GC含量')
            axes[1, 1].set_xlabel('序列长度 (bp)')
            axes[1, 1].set_ylabel('GC含量 (%)')
            
            # 添加相关系数
            correlation = np.corrcoef(lengths, gc_contents)[0, 1]
            axes[1, 1].text(0.05, 0.95, f'相关系数: {correlation:.3f}', 
                           transform=axes[1, 1].transAxes, fontsize=10,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))
            
            plt.tight_layout()
            
            # 保存图表
            plot_file = self.output_dir / f"{gene_type}_analysis.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"保存图表: {plot_file}")
            
        except ImportError:
            self.logger.warning("matplotlib/seaborn未安装，跳过可视化")
        except Exception as e:
            self.logger.error(f"创建可视化失败: {e}")
    
    def analyze_all_sequences(self):
        """分析所有序列文件"""
        self.logger.info("开始分析所有保守序列文件")
        
        # 查找所有FASTA文件
        fasta_files = list(self.sequences_dir.glob("*.fasta"))
        if not fasta_files:
            self.logger.error(f"在 {self.sequences_dir} 中未找到FASTA文件")
            return
        
        self.logger.info(f"找到 {len(fasta_files)} 个FASTA文件")
        
        # 分析每个文件
        for fasta_file in fasta_files:
            gene_type = fasta_file.stem.replace('_sequences', '')
            stats = self.analyze_sequence_file(fasta_file)
            
            if stats:
                self.analysis_results[gene_type] = stats
                
                # 创建可视化
                self.create_visualizations(gene_type, stats)
        
        # 生成综合报告
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """生成综合分析报告"""
        report_file = self.output_dir / "comprehensive_analysis_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("保守序列综合分析报告\n")
            f.write("=" * 60 + "\n\n")
            f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"分析文件数: {len(self.analysis_results)}\n\n")
            
            # 总体统计
            total_sequences = sum(stats['total_sequences'] for stats in self.analysis_results.values())
            total_genomes = len(set().union(*[set(stats['genome_counts'].keys()) 
                                            for stats in self.analysis_results.values()]))
            
            f.write("总体统计:\n")
            f.write("-" * 30 + "\n")
            f.write(f"总序列数: {total_sequences}\n")
            f.write(f"涉及基因组数: {total_genomes}\n\n")
            
            # 各基因类型详细统计
            f.write("各基因类型详细统计:\n")
            f.write("-" * 30 + "\n")
            
            for gene_type, stats in self.analysis_results.items():
                f.write(f"\n{gene_type}:\n")
                f.write(f"  序列数量: {stats['total_sequences']}\n")
                f.write(f"  基因组数: {stats['unique_genomes']}\n")
                f.write(f"  长度范围: {stats['length_stats']['min']}-{stats['length_stats']['max']} bp\n")
                f.write(f"  平均长度: {stats['length_stats']['mean']:.1f} ± {stats['length_stats']['std']:.1f} bp\n")
                f.write(f"  GC含量范围: {stats['gc_stats']['min']:.1f}-{stats['gc_stats']['max']:.1f}%\n")
                f.write(f"  平均GC含量: {stats['gc_stats']['mean']:.1f} ± {stats['gc_stats']['std']:.1f}%\n")
            
            # 基因组覆盖度分析
            f.write(f"\n基因组覆盖度分析:\n")
            f.write("-" * 30 + "\n")
            
            genome_gene_counts = defaultdict(int)
            for gene_type, stats in self.analysis_results.items():
                for genome_id in stats['genome_counts']:
                    genome_gene_counts[genome_id] += 1
            
            coverage_dist = Counter(genome_gene_counts.values())
            for gene_count, genome_count in sorted(coverage_dist.items()):
                f.write(f"  {gene_count} 种基因: {genome_count} 个基因组\n")
        
        self.logger.info(f"生成综合报告: {report_file}")
        
        # 生成CSV格式的详细数据
        self.export_detailed_data()
    
    def export_detailed_data(self):
        """导出详细数据到CSV"""
        all_data = []
        
        for gene_type, stats in self.analysis_results.items():
            for seq_info in stats['sequences']:
                row = {
                    'gene_type': gene_type,
                    'sequence_id': seq_info['id'],
                    'genome_id': seq_info.get('genome_id', 'Unknown'),
                    'length': seq_info['length'],
                    'gc_content': seq_info['gc_content'],
                    'description': seq_info['description']
                }
                all_data.append(row)
        
        if all_data:
            df = pd.DataFrame(all_data)
            csv_file = self.output_dir / "sequence_details.csv"
            df.to_csv(csv_file, index=False)
            self.logger.info(f"导出详细数据: {csv_file}")
            
            # 生成汇总统计
            summary_stats = df.groupby('gene_type').agg({
                'length': ['count', 'mean', 'std', 'min', 'max'],
                'gc_content': ['mean', 'std', 'min', 'max'],
                'genome_id': 'nunique'
            }).round(2)
            
            summary_file = self.output_dir / "summary_statistics.csv"
            summary_stats.to_csv(summary_file)
            self.logger.info(f"导出汇总统计: {summary_file}")

def main():
    parser = argparse.ArgumentParser(description='分析保守序列')
    parser.add_argument('--sequences-dir', default='conserved_sequences_test', 
                       help='保守序列目录')
    parser.add_argument('--output-dir', default='sequence_analysis', 
                       help='分析结果输出目录')
    
    args = parser.parse_args()
    
    # 检查输入目录
    if not Path(args.sequences_dir).exists():
        print(f"错误: 序列目录不存在: {args.sequences_dir}")
        return
    
    # 创建分析器
    analyzer = ConservedSequenceAnalyzer(
        sequences_dir=args.sequences_dir,
        output_dir=args.output_dir
    )
    
    # 开始分析
    analyzer.analyze_all_sequences()
    
    print(f"分析完成! 结果保存在: {args.output_dir}")

if __name__ == "__main__":
    main()
