#!/usr/bin/env python3
"""
安装保守序列提取所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {package} - {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        print(f"✅ 已安装: {package}")
        return True
    except ImportError:
        print(f"⚠️  未安装: {package}")
        return False

def main():
    print("检查和安装保守序列提取所需的依赖包...")
    print("=" * 50)
    
    # 需要的包
    required_packages = {
        'Bio': 'biopython',  # 包名映射
        'pandas': 'pandas',
        'numpy': 'numpy'
    }
    
    missing_packages = []
    
    # 检查已安装的包
    for import_name, package_name in required_packages.items():
        if not check_package(import_name):
            missing_packages.append(package_name)
    
    # 安装缺失的包
    if missing_packages:
        print(f"\n需要安装 {len(missing_packages)} 个包...")
        for package in missing_packages:
            print(f"正在安装: {package}")
            install_package(package)
    else:
        print("\n所有依赖包都已安装!")
    
    print("\n验证安装...")
    print("-" * 30)
    
    # 验证安装
    try:
        from Bio import SeqIO
        from Bio.Seq import Seq
        from Bio.SeqRecord import SeqRecord
        import pandas as pd
        import numpy as np
        print("✅ 所有包导入成功!")
        
        # 测试BioPython基本功能
        test_seq = Seq("ATCGATCGATCG")
        test_record = SeqRecord(test_seq, id="test", description="test sequence")
        print("✅ BioPython功能测试通过!")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请手动安装缺失的包:")
        print("pip install biopython pandas numpy")

if __name__ == "__main__":
    main()
