#!/usr/bin/env python3
"""
基因组特征提取示例脚本
演示如何使用优化的基因组特征提取器
"""

import subprocess
import sys
import os
from pathlib import Path
import pandas as pd

def check_environment():
    """检查环境和依赖"""
    print("=== 环境检查 ===")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查关键包
    packages = {
        'Bio': 'BioPython',
        'pandas': 'Pandas', 
        'numpy': 'NumPy',
        'matplotlib': 'Matplotlib'
    }
    
    missing_packages = []
    
    for module, name in packages.items():
        try:
            __import__(module)
            print(f"✅ {name}: 已安装")
        except ImportError:
            print(f"❌ {name}: 未安装")
            missing_packages.append(name)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("bash setup_conda_environment.sh")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_data_structure():
    """检查数据目录结构"""
    print("\n=== 数据结构检查 ===")
    
    # 检查metadata文件
    metadata_file = "data_downloads/metadata-v2.tsv"
    if not os.path.exists(metadata_file):
        print(f"❌ metadata文件不存在: {metadata_file}")
        return False
    
    print(f"✅ metadata文件存在: {metadata_file}")
    
    # 检查数据目录
    data_dir = Path("data_downloads")
    if not data_dir.exists():
        print("❌ data_downloads目录不存在")
        return False
    
    # 统计基因组数量
    genome_count = 0
    categories = ['Bacteria', 'Archaea', 'Fungi', 'Algae', 'Protist', 'Virus']
    
    for category in categories:
        category_dir = data_dir / category
        if category_dir.exists():
            genomes = [d for d in category_dir.iterdir() if d.is_dir()]
            if genomes:
                print(f"✅ {category}: {len(genomes)} 个基因组")
                genome_count += len(genomes)
    
    if genome_count == 0:
        print("❌ 未找到基因组数据")
        return False
    
    print(f"✅ 总计: {genome_count} 个基因组")
    
    # 检查文件格式
    print("\n检查文件格式...")
    sample_found = False
    
    for category in categories:
        category_dir = data_dir / category
        if category_dir.exists():
            for genome_dir in category_dir.iterdir():
                if genome_dir.is_dir():
                    genome_id = genome_dir.name
                    
                    # 检查序列文件
                    fna_files = list(genome_dir.glob(f"{genome_id}_genomic.fna*"))
                    gff_files = list(genome_dir.glob(f"{genome_id}_genomic.gff*")) + \
                               list(genome_dir.glob(f"{genome_id}_genomic.gtf*"))
                    
                    if fna_files and gff_files:
                        print(f"✅ 样本检查 {genome_id}: 序列文件和注释文件都存在")
                        sample_found = True
                        break
            
            if sample_found:
                break
    
    if not sample_found:
        print("⚠️  未找到完整的样本文件（序列+注释）")
    
    return True

def run_test_extraction():
    """运行测试提取"""
    print("\n=== 测试特征提取 ===")
    
    # 询问用户
    response = input("是否运行测试提取 (前3个基因组)? [y/N]: ").strip().lower()
    
    if response not in ['y', 'yes']:
        print("跳过测试提取")
        return
    
    print("开始测试提取...")
    
    # 构建命令
    cmd = [
        sys.executable,
        "optimized_genome_feature_extractor.py",
        "data_downloads/metadata-v2.tsv",
        "--max-genomes", "3",
        "--max-workers", "2",
        "--output-file", "test_genome_features.csv"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 执行提取
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 测试提取成功!")
        
        # 检查输出文件
        if os.path.exists("test_genome_features.csv"):
            df = pd.read_csv("test_genome_features.csv")
            print(f"✅ 生成特征文件: {len(df)} 行, {len(df.columns)} 列")
            
            # 显示部分特征
            print("\n提取的特征类型:")
            feature_types = []
            for col in df.columns:
                if col.startswith('genome_'):
                    feature_types.append('基因组基本特征')
                elif col.startswith('cds_'):
                    feature_types.append('CDS特征')
                elif col.startswith('trna_'):
                    feature_types.append('tRNA特征')
                elif col.startswith('rrna_'):
                    feature_types.append('rRNA特征')
                elif col.startswith('aa_'):
                    feature_types.append('氨基酸特征')
                elif col.startswith('codon_'):
                    feature_types.append('密码子特征')
                elif col.startswith('dinuc_'):
                    feature_types.append('二核苷酸特征')
            
            from collections import Counter
            type_counts = Counter(feature_types)
            for ftype, count in type_counts.items():
                print(f"  {ftype}: {count} 个")
        
        # 检查报告文件
        report_file = "test_genome_features_report.txt"
        if os.path.exists(report_file):
            print(f"\n✅ 生成处理报告: {report_file}")
            with open(report_file, 'r', encoding='utf-8') as f:
                print(f.read())
    
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试提取失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout)
        if e.stderr:
            print("错误输出:", e.stderr)
    except Exception as e:
        print(f"❌ 执行失败: {e}")

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===")
    
    print("1. 基本使用:")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv")
    
    print("\n2. 测试模式 (前10个基因组):")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-genomes 10")
    
    print("\n3. 多线程处理:")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --max-workers 6")
    
    print("\n4. 指定输出文件:")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --output-file my_features.csv")
    
    print("\n5. 禁用多线程:")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv --no-threading")
    
    print("\n6. 完整参数示例:")
    print("   python optimized_genome_feature_extractor.py data_downloads/metadata-v2.tsv \\")
    print("       --max-workers 4 \\")
    print("       --output-file genome_features_full.csv \\")
    print("       --base-dir data_downloads")

def show_feature_description():
    """显示特征描述"""
    print("\n=== 提取的特征类型 ===")
    
    features = {
        "基因组基本特征": [
            "genome_size - 基因组大小",
            "gc_content - GC含量",
            "at_skew - AT偏斜",
            "dinuc_* - 二核苷酸频率",
            "cpg_oe_ratio - CpG观察/期望比值"
        ],
        "CDS特征": [
            "total_cds_count - CDS总数",
            "avg_cds_length - 平均CDS长度", 
            "cds_gc_content - CDS的GC含量",
            "enc - 有效密码子数",
            "gc1/gc2/gc3 - 密码子各位置GC含量"
        ],
        "氨基酸特征": [
            "aa_* - 各氨基酸频率",
            "hydrophobic_ratio - 疏水性氨基酸比例",
            "charged_ratio - 带电氨基酸比例",
            "avg_molecular_weight - 平均分子量",
            "aromatic_ratio - 芳香族氨基酸比例"
        ],
        "密码子特征": [
            "codon_* - 各密码子频率",
            "cub - 密码子使用偏好"
        ],
        "RNA特征": [
            "total_trna_count - tRNA总数",
            "avg_trna_length - 平均tRNA长度",
            "total_rrna_count - rRNA总数",
            "rrna_16s_count - 16S rRNA数量"
        ],
        "结构特征": [
            "gene_density - 基因密度",
            "avg_intergenic_distance - 平均基因间距",
            "strand_bias - 链偏好"
        ]
    }
    
    for category, feature_list in features.items():
        print(f"\n{category}:")
        for feature in feature_list:
            print(f"  • {feature}")

def main():
    """主函数"""
    print("基因组特征提取器使用示例")
    print("=" * 50)
    
    # 环境检查
    if not check_environment():
        return
    
    # 数据结构检查
    if not check_data_structure():
        return
    
    # 运行测试
    run_test_extraction()
    
    # 显示使用示例
    show_usage_examples()
    
    # 显示特征描述
    show_feature_description()
    
    print("\n" + "=" * 50)
    print("示例完成!")

if __name__ == "__main__":
    main()
